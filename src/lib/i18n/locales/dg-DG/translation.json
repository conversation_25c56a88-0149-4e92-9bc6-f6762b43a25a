{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' or '-1' for no expire. Much permanent, very wow.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(such e.g. `sh webui.sh --api`)", "(latest)": "(much latest)", "{{ models }}": "", "{{COUNT}} Replies": "", "{{user}}'s Chats": "", "{{webUIName}} Backend Required": "{{webUIName}} Backend Much Required", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "", "a user": "such user", "About": "Much About", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "Account", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "", "Add a model ID": "", "Add a short description about what this model does": "", "Add a tag": "Add such tag", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "", "Add Files": "Add Files", "Add Group": "", "Add Memory": "", "Add Model": "", "Add Reaction": "", "Add Tag": "", "Add Tags": "", "Add text content": "", "Add User": "", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Adjusting these settings will apply changes to all users. Such universal, very wow.", "admin": "admin", "Admin": "", "Admin Panel": "Admin Panel", "Admin Settings": "<PERSON><PERSON>s", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "Advanced Parameters", "Advanced Params": "", "All Documents": "", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Allow Delete Chats", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "Such account exists?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "such assistant", "and": "and", "and {{COUNT}} more": "", "and create a new shared link.": "", "API Base URL": "API Base URL", "API Key": "API Key", "API Key created.": "", "API Key Endpoint Restrictions": "", "API keys": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "", "Archive": "", "Archive All Chats": "", "Archived Chats": "", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Such certainty?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "Attach file", "Attribute for Username": "", "Audio": "Audio", "August": "", "Authenticate": "", "Auto-Copy Response to Clipboard": "Copy Bark Auto Bark", "Auto-playback response": "Auto-playback response", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 Base URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Base URL is required.", "Available list": "", "available!": "available! So excite!", "Azure AI Speech": "", "Azure Region": "", "Back": "Back", "Bad": "", "Bad Response": "", "Banners": "", "Base Model (From)": "", "Batch Size (num_batch)": "", "before": "", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "", "By {{name}}": "", "Bypass SSL verification for Websites": "", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "Cancel", "Capabilities": "", "Capture": "", "Certificate Path": "", "Change Password": "Change Password", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Cha<PERSON>", "Chat Background Image": "", "Chat Bubble UI": "", "Chat Controls": "", "Chat direction": "", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Chats", "Check Again": "Check Again", "Check for updates": "Check for updates", "Checking for updates...": "Checking for updates... Such anticipation...", "Choose a model before saving...": "Choose model before saving... Wow choose first.", "Chunk Overlap": "<PERSON><PERSON>", "Chunk Params": "Chunk Params", "Chunk Size": "Chunk Size", "Ciphers": "", "Citation": "", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Click for help. Much assist.", "Click here to": "", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Click to select", "Click here to select a csv file.": "", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "click here. Such click.", "Click on the user role button to change a user's role.": "Click user role button to change role.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "", "Close": "Close", "Code execution": "", "Code formatted successfully": "", "Collection": "Collection", "Color": "", "ComfyUI": "", "ComfyUI API Key": "", "ComfyUI Base URL": "", "ComfyUI Base URL is required.": "", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Command", "Completions": "", "Concurrent Requests": "", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "Confirm Password", "Confirm your action": "", "Confirm your new password": "", "Connections": "Connections", "Contact Admin for WebUI Access": "", "Content": "Content", "Content Extraction": "", "Context Length": "Context Length", "Continue Response": "", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "", "Copied to clipboard": "", "Copy": "", "Copy last code block": "Copy last code block", "Copy last response": "Copy last response", "Copy Link": "", "Copy to clipboard": "", "Copying to clipboard was successful!": "Copying to clipboard was success! Very success!", "Create": "", "Create a knowledge base": "", "Create a model": "", "Create Account": "Create Account", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "", "Create new secret key": "", "Created at": "Created at", "Created At": "", "Created by": "", "CSV Import": "", "Current Model": "Current Model", "Current Password": "Current Password", "Custom": "Custom", "Dark": "Dark", "Database": "Database", "December": "", "Default": "<PERSON><PERSON><PERSON>", "Default (Open AI)": "", "Default (SentenceTransformers)": "", "Default Model": "", "Default model updated": "Default model much updated", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Default Prompt Suggestions", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Default User Role", "Delete": "", "Delete a model": "Delete a model", "Delete All Chats": "", "Delete All Models": "", "Delete chat": "Delete chat", "Delete Chat": "", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "", "Delete tool?": "", "Delete User": "", "Deleted {{deleteModelTag}}": "Deleted {{deleteModelTag}}", "Deleted {{name}}": "", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Description", "Disabled": "", "Discover a function": "", "Discover a model": "", "Discover a prompt": "Discover a prompt", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "Discover, download, and explore custom prompts", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "Discover, download, and explore model presets", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "Display username instead of You in Chat", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Document", "Documentation": "", "Documents": "Documents", "does not make any external connections, and your data stays securely on your locally hosted server.": "does not connect external, data stays safe locally.", "Don't have an account?": "No account? Much sad.", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "", "Download canceled": "", "Download Database": "Download Database", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Drop files here to add to conversation", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "e.g. '30s','10m'. Much time units are 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "<PERSON>", "Edit User Group": "", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "", "Embedding Model Engine": "", "Embedding model set to \"{{embedding_model}}\"": "", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Enable New Bark Ups", "Enable Web Search": "", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "", "Enter {{role}} message here": "Enter {{role}} bork here", "Enter a detail about yourself for your LLMs to recall": "", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "<PERSON><PERSON> of Chunks", "Enter Chunk Size": "<PERSON><PERSON> of Chunk", "Enter description": "", "Enter Github Raw URL": "", "Enter Google PSE API Key": "", "Enter Google PSE Engine Id": "", "Enter Image Size (e.g. 512x512)": "Enter Size of Wow (e.g. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Enter model doge tag (e.g. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Enter Number of Steps (e.g. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "", "Enter Seed": "", "Enter Serper API Key": "", "Enter Serply API Key": "", "Enter Serpstack API Key": "", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Enter stop bark", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Enter Top Wow", "Enter URL (e.g. http://127.0.0.1:7860/)": "Enter URL (e.g. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "", "Enter your current password": "", "Enter Your Email": "Enter Your Dogemail", "Enter Your Full Name": "Enter Your Full Wow", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "Enter Your Barkword", "Enter your prompt": "", "Enter Your Role": "", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Much Experiment", "Explore the cosmos": "", "Export": "", "Export All Archived Chats": "", "Export All Chats (All Users)": "Export All Chats (All Doggos)", "Export chat (.json)": "", "Export Chats": "Export Barks", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "", "Export Presets": "", "Export Prompts": "Export Promptos", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "", "Failed to read clipboard contents": "Failed to read clipboard borks", "Failed to save models configuration": "", "Failed to update settings": "", "February": "", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "Bark Mode", "File not found.": "Bark not found.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingerprint dogeing: Unable to use initials as avatar. Defaulting to default doge image.", "Fluidly stream large external response chunks": "Fluidly wow big chunks", "Focus chat input": "Focus chat bork", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "Woweral", "General Settings": "General <PERSON><PERSON>", "Generate Image": "", "Generating search query": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "", "Google Drive": "", "Google PSE API Key": "", "Google PSE Engine Id": "", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "", "Hello, {{name}}": "Much helo, {{name}}", "Help": "", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON>de", "Host": "", "How can I help you today?": "How can I halp u today?", "How would you rate this response?": "", "Hybrid Search": "", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Image Wow (Much Experiment)", "Image Generation Engine": "Image Engine", "Image Max Compression Size": "", "Image Settings": "Settings for Wowmage", "Images": "Wowmages", "Import Chats": "Import Barks", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "", "Import Presets": "", "Import Prompts": "Import Promptos", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "Include `--api` flag when running stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "", "Input commands": "Input commands", "Install from Github URL": "", "Instant Auto-Send After Voice Transcription": "", "Interface": "Interface", "Invalid file format.": "", "Invalid Tag": "", "is typing...": "", "January": "", "Jina API Key": "", "join our Discord for help.": "join our Discord for help.", "JSON": "JSON", "JSON Preview": "", "July": "", "June": "", "JWT Expiration": "JWT Expire", "JWT Token": "JWT Borken", "Kagi Search API Key": "", "Keep Alive": "Keep Wow", "Key": "", "Keyboard shortcuts": "Keyboard Barkcuts", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "<PERSON><PERSON>", "Last Active": "", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "Light", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "", "Made by OpenWebUI Community": "Made by OpenWebUI Community", "Make sure to enclose them with": "Make sure to enclose them with", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "", "March": "", "Max Tokens (num_predict)": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maximum of 3 models can be downloaded simultaneously. Please try again later.", "May": "", "Memories accessible by LLMs will be shown here.": "", "Memory": "", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "", "Min P": "", "Minimum Score": "", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' has been successfully downloaded.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' is already in queue for downloading.", "Model {{modelId}} not found": "Model {{modelId}} not found", "Model {{modelName}} is not vision capable": "", "Model {{name}} is now {{status}}": "", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Model filesystem bark detected. Model shortname is required for update, cannot continue.", "Model Filtering": "", "Model ID": "", "Model IDs": "", "Model Name": "", "Model not selected": "Model not selected", "Model Params": "", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "Modelfile Content", "Models": "Wowdels", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "", "Name": "Name", "Name your knowledge base": "", "New Chat": "New Bark", "New folder": "", "New Password": "New Barkword", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "", "No search query generated": "", "No source available": "No source available", "No users were found.": "", "No valves to update": "", "None": "", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notifications", "November": "", "num_gpu (Ollama)": "", "num_thread (Ollama)": "", "OAuth ID": "", "October": "", "Off": "Off", "Okay, Let's Go!": "Okay, Let's Go!", "OLED Dark": "OLED Dark", "Ollama": "", "Ollama API": "", "Ollama API disabled": "", "Ollama API settings updated": "", "Ollama Version": "Ollama Version", "On": "On", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Only wow characters and hyphens are allowed in the bork string.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oops! Looks like the URL is invalid. Please double-check and try again.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.", "Open in full screen": "", "Open new chat": "Open new bark", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "", "OpenAI API": "OpenAI API", "OpenAI API Config": "", "OpenAI API Key is required.": "OpenAI Bark Key is required.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "", "or": "or", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "Barkword", "Paste Large Text as File": "", "PDF document (.pdf)": "", "PDF Extract Images (OCR)": "PDF Extract Wowmages (OCR)", "pending": "pending", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "Permission denied when accessing microphone: {{error}}", "Permissions": "", "Personalization": "Personalization", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "", "Pipelines Not Detected": "", "Pipelines Valves": "", "Plain text (.txt)": "Plain text (.txt)", "Playground": "Playground", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "", "Previous 7 days": "", "Profile Image": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "", "Prompt Content": "Prompt Content", "Prompt created successfully": "", "Prompt suggestions": "Prompt wowgestions", "Prompt updated successfully": "", "Prompts": "<PERSON>mpt<PERSON>", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "", "Pull a model from Ollama.com": "Pull a wowdel from Ollama.com", "Query Generation Prompt": "", "Query Params": "Query Bark", "RAG Template": "RAG Template", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "", "Record voice": "Record Bark", "Redirecting you to OpenWebUI Community": "Redirecting you to OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "", "Release Notes": "Release Borks", "Relevance": "", "Remove": "", "Remove Model": "", "Rename": "", "Reorder Models": "", "Repeat Last N": "Repeat Last N", "Reply in Thread": "", "Request Mode": "Request Bark", "Reranking Model": "", "Reranking model disabled": "", "Reranking model set to \"{{reranking_model}}\"": "", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Role", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "", "Run": "", "Running": "", "Save": "Save much wow", "Save & Create": "Save & Create much create", "Save & Update": "Save & Update much update", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Saving chat logs in browser storage not support anymore. Pls download and delete your chat logs by click button below. Much easy re-import to backend through", "Scroll to bottom when switching between branches": "", "Search": "Search very search", "Search a model": "", "Search Base": "", "Search Chats": "", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "", "Search options": "", "Search Prompts": "Search Prompts much wow", "Search Result Count": "", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "", "See readme.md for instructions": "See readme.md for instructions wow", "See what's new": "See what's new so amaze", "Seed": "Seed very plant", "Select a base model": "", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "Select a model much choice", "Select a pipeline": "", "Select a pipeline url": "", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "Select model much choice", "Select only one model to call": "", "Selected model(s) do not support image inputs": "", "Semantic distance to query": "", "Send": "", "Send a message": "", "Send a Message": "Send a Message much message", "Send message": "Send message very send", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "", "Serper API Key": "", "Serply API Key": "", "Serpstack API Key": "", "Server connection verified": "Server connection verified much secure", "Set as default": "Set as default very default", "Set CFG Scale": "", "Set Default Model": "Set Default Model much model", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "", "Set Image Size": "Set Image Size very size", "Set reranking model (e.g. {{model}})": "", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Set Steps so many steps", "Set Task Model": "", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Set Voice so speak", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Settings much settings", "Settings saved successfully!": "Settings saved successfully! Very success!", "Share": "", "Share Chat": "", "Share to OpenWebUI Community": "Share to OpenWebUI Community much community", "Show": "Show much show", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "Show shortcuts much shortcut", "Show your support!": "", "Sign in": "Sign in very sign", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Sign Out much logout", "Sign up": "Sign up much join", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Source", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Speech recognition error: {{error}} so error", "Speech-to-Text Engine": "Speech-to-Text Engine much speak", "Stop": "", "Stop Sequence": "Stop Sequence much stop", "Stream Chat Response": "", "STT Model": "", "STT Settings": "STT Settings very settings", "Success": "Success very success", "Successfully updated.": "Successfully updated. Very updated.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "System very system", "System Instructions": "", "System Prompt": "System Prompt much prompt", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "Temperature very temp", "Template": "Template much template", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "Text-to-Speech Engine much speak", "Tfs Z": "Tfs Z much Z", "Thanks for your feedback!": "", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "Theme much theme", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "This ensures that your valuable conversations are securely saved to your backend database. Thank you! Much secure!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement. Much tip!", "Title": "Title very title", "Title (e.g. Tell me a fun fact)": "", "Title Auto-Generation": "Title Auto-Generation much auto-gen", "Title cannot be an empty string.": "", "Title Generation Prompt": "Title Generation Prompt very prompt", "TLS": "", "To access the available model names for downloading,": "To access the available model names for downloading, much access", "To access the GGUF models available for downloading,": "To access the GGUF models available for downloading, much access", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "", "Toggle settings": "Toggle settings much toggle", "Toggle sidebar": "Toggle sidebar much toggle", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K very top", "Top P": "Top P very top", "Transformers": "", "Trouble accessing Ollama?": "Trouble accessing Ollama? Much trouble?", "TTS Model": "", "TTS Settings": "TTS Settings much settings", "TTS Voice": "", "Type": "", "Type Hugging Face Resolve (Download) URL": "Type Hugging Face Resolve (Download) URL much download", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "", "Update for the latest features and improvements.": "", "Update password": "Update password much change", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "Upload a GGUF model very upload", "Upload directory": "", "Upload files": "", "Upload Files": "", "Upload Pipeline": "", "Upload Progress": "Upload Progress much progress", "URL": "", "URL Mode": "URL Mode much mode", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "Use Gravatar much avatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "Use Initials much initial", "use_mlock (Ollama)": "", "use_mmap (Ollama)": "", "user": "user much user", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "Users much users", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "Utilize very use", "Valid time units:": "Valid time units: much time", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "variable very variable", "variable to have them replaced with clipboard content.": "variable to have them replaced with clipboard content. Very replace.", "Version": "Version much version", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "", "Web": "Web very web", "Web API": "", "Web Loader Settings": "", "Web Search": "", "Web Search Engine": "", "Web Search Query Generation": "", "Webhook URL": "", "WebUI Settings": "WebUI Settings much settings", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "What’s New in much new", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Write a prompt suggestion (e.g. Who are you?) much suggest", "Write a summary in 50 words that summarizes [topic or keyword].": "Write a summary in 50 words that summarizes [topic or keyword]. Much summarize.", "Write something...": "", "Write your model template content here": "", "Yesterday": "", "You": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "", "You have shared this chat": "", "You're a helpful assistant.": "You're a helpful assistant. Much helpful.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "", "Youtube Loader Settings": ""}