{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' lub '-1' dla bez wygaśnię<PERSON>.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(np. `sh webui.sh --api`)", "(latest)": "(na<PERSON><PERSON><PERSON>)", "{{ models }}": "{{ modele }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} - czaty", "{{webUIName}} Backend Required": "Backend {{webUIName}} wymagane", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Model zada<PERSON> jest używany podczas wykonywania zadań, takich jak generowanie tytułów czatów i zapytań wyszukiwania w Internecie", "a user": "użytkownik", "About": "O nas", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "Ko<PERSON>", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "<PERSON><PERSON><PERSON>", "Add a model ID": "", "Add a short description about what this model does": "Dodaj krótki opis działania tego modelu", "Add a tag": "<PERSON><PERSON><PERSON> tag", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "Do<PERSON>j wła<PERSON>ne <PERSON>nie", "Add Files": "<PERSON><PERSON>j pliki", "Add Group": "", "Add Memory": "<PERSON><PERSON><PERSON>", "Add Model": "Dodaj model", "Add Reaction": "", "Add Tag": "", "Add Tags": "<PERSON><PERSON><PERSON> tagi", "Add text content": "", "Add User": "Dodaj użytkownika", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Dostosowanie tych ustawień spowoduje zastosowanie zmian uniwersalnie do wszystkich użytkowników.", "admin": "admin", "Admin": "", "Admin Panel": "Panel administracyjny", "Admin Settings": "Ustawienia administratora", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> parametry", "Advanced Params": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> parametry", "All Documents": "Wszystkie dokumenty", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Pozwól na usuwanie czatu", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "Masz już konto?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "asystent", "and": "i", "and {{COUNT}} more": "", "and create a new shared link.": "i utwórz nowy udostępniony link", "API Base URL": "Podstawowy adres URL interfejsu API", "API Key": "Klucz API", "API Key created.": "Klucz API utworzony.", "API Key Endpoint Restrictions": "", "API keys": "Klucze API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "Kwiecień", "Archive": "Archiwu<PERSON>", "Archive All Chats": "Archiwizuj wszystkie czaty", "Archived Chats": "Zarchiwizowane czaty", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "<PERSON><PERSON><PERSON> pewien?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "Dołącz plik", "Attribute for Username": "", "Audio": "Dźwięk", "August": "Sierpień", "Authenticate": "", "Auto-Copy Response to Clipboard": "Automatyczne kopiowanie odpowiedzi do schowka", "Auto-playback response": "Odtwarzanie automatyczne odpowiedzi", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "Podstawowy adres URL AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Podstawowy adres URL AUTOMATIC1111 jest wymagany.", "Available list": "", "available!": "dostę<PERSON>ny!", "Azure AI Speech": "", "Azure Region": "", "Back": "Wstecz", "Bad": "", "Bad Response": "Zła odpowiedź", "Banners": "Banery", "Base Model (From)": "<PERSON> podstawowy (od)", "Batch Size (num_batch)": "", "before": "przed", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Klucz API wyszukiwania Brave", "By {{name}}": "", "Bypass SSL verification for Websites": "Pomiń weryfikację SSL dla stron webowych", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "<PERSON><PERSON><PERSON><PERSON>ści", "Capture": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON><PERSON> hasło", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "<PERSON><PERSON><PERSON>", "Chat Background Image": "", "Chat Bubble UI": "Bąbelki czatu", "Chat Controls": "", "Chat direction": "<PERSON><PERSON><PERSON><PERSON>", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "<PERSON><PERSON><PERSON>", "Check Again": "Sprawdź ponownie", "Check for updates": "Sprawdź aktualizacje", "Checking for updates...": "Sprawdzanie aktualizacji...", "Choose a model before saving...": "Wybierz model przed zapisaniem...", "Chunk Overlap": "Zach<PERSON><PERSON><PERSON> bloku", "Chunk Params": "Parametry bloku", "Chunk Size": "Roz<PERSON>r bloku", "Ciphers": "", "Citation": "Cytat", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "<PERSON><PERSON><PERSON><PERSON> tutaj, aby uzyskać pomoc.", "Click here to": "Kliknij tutaj, żeby", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "<PERSON><PERSON><PERSON><PERSON> tutaj, a<PERSON> w<PERSON><PERSON>", "Click here to select a csv file.": "Kliknij tutaj, żeby wybrać plik CSV", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "k<PERSON><PERSON><PERSON> tutaj.", "Click on the user role button to change a user's role.": "Kliknij przycisk roli użytkownika, aby zmienić rolę użytkownika.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "Klon", "Close": "Zamknij", "Code execution": "", "Code formatted successfully": "", "Collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "Bazowy URL ComfyUI", "ComfyUI Base URL is required.": "Bazowy URL ComfyUI jest wymagany.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Polecenie", "Completions": "", "Concurrent Requests": "Równoczesne żądania", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "Potwierd<PERSON> hasło", "Confirm your action": "", "Confirm your new password": "", "Connections": "Połączenia", "Contact Admin for WebUI Access": "", "Content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Content Extraction": "", "Context Length": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> konte<PERSON>", "Continue Response": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> od<PERSON>wiedź", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "Skopiowano URL czatu do schowka!", "Copied to clipboard": "", "Copy": "<PERSON><PERSON><PERSON><PERSON>", "Copy last code block": "Skopiuj ostatni blok kodu", "Copy last response": "Skopiuj ostatnią odpowiedź", "Copy Link": "Kopiuj link", "Copy to clipboard": "", "Copying to clipboard was successful!": "Kopiowanie do schowka zakończone powodzeniem!", "Create": "", "Create a knowledge base": "", "Create a model": "Tworzenie modelu", "Create Account": "Utwórz konto", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Utwórz nowy klucz", "Create new secret key": "Utwórz nowy klucz bezpieczeństwa", "Created at": "Utworzono o", "Created At": "Utworzono o", "Created by": "", "CSV Import": "", "Current Model": "Bież<PERSON><PERSON> model", "Current Password": "Bieżące hasło", "Custom": "Niestandardowy", "Dark": "Ciemny", "Database": "<PERSON><PERSON> danych", "December": "Grudzień", "Default": "Domyślny", "Default (Open AI)": "", "Default (SentenceTransformers)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (SentenceTransformers)", "Default Model": "<PERSON> do<PERSON>ślny", "Default model updated": "Domyślny model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Domyślne sugestie promptów", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Domyślna rola użytkownika", "Delete": "Usuń", "Delete a model": "Usuń model", "Delete All Chats": "Usuń wszystkie czaty", "Delete All Models": "", "Delete chat": "<PERSON><PERSON><PERSON> c<PERSON>t", "Delete Chat": "<PERSON><PERSON><PERSON> c<PERSON>t", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "usuń ten link", "Delete tool?": "", "Delete User": "Usuń użytkownika", "Deleted {{deleteModelTag}}": "Usunięto {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON><PERSON><PERSON> {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Opis", "Disabled": "", "Discover a function": "", "Discover a model": "Odk<PERSON><PERSON> model", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> prompt", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pobierz i eksploruj niestandardowe prompty", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pobierz i eksploruj ustawienia modeli", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "Wyświetl nazwę użytkownika zamiast Ty w czacie", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Dokument", "Documentation": "", "Documents": "Dokumenty", "does not make any external connections, and your data stays securely on your locally hosted server.": "nie nawią<PERSON>je żadnych zewnętrznych połączeń, a Twoje dane pozostają bezpiecznie na Twoim lokalnie hostowanym serwerze.", "Don't have an account?": "Nie masz konta?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "Pobieranie", "Download canceled": "Pobieranie przerwane", "Download Database": "Pobierz bazę danych", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Upuść pliki tutaj, aby dodać do rozmowy", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "np. '30s', '10m'. Poprawne jednostki czasu to 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "Edytuj użytkownika", "Edit User Group": "", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Embedding Model Engine": "Silnik modelu osadzania", "Embedding model set to \"{{embedding_model}}\"": "Model osadzania ustawiono na \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Włączanie udostępniania społecznościowego", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Włącz nowe rejestracje", "Enable Web Search": "Włączanie wyszukiwania w Internecie", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Upewnij się, że twój plik CSV zawiera 4 kolumny w następującym porządku: Nazwa, Email, Hasło, Rola.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON><PERSON><PERSON> wiadom<PERSON>ść {{role}} tutaj", "Enter a detail about yourself for your LLMs to recall": "Wprowadź szczegóły o sobie, aby LLMs mogli p<PERSON>", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Wprowadź klucz API Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Wprowadź zakchodzenie bloku", "Enter Chunk Size": "Wprowadź rozmiar bloku", "Enter description": "", "Enter Github Raw URL": "Wprowadź nieprzetworzony adres URL usługi Github", "Enter Google PSE API Key": "Wprowadź klucz API Google PSE", "Enter Google PSE Engine Id": "Wprowadź identyfikator aparatu Google PSE", "Enter Image Size (e.g. 512x512)": "Wprowadź rozmiar obrazu (np. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Wprowadź kody języków", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Wprowadź tag modelu (np. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON>ź liczbę kroków (np. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Wprowadź wynik", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Wprowadź adres URL zapytania Searxng", "Enter Seed": "", "Enter Serper API Key": "Wprowadź klucz API Serper", "Enter Serply API Key": "", "Enter Serpstack API Key": "Wprowadź klucz API Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Wprowadź sekwencję zatrzymania", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Wprowadź Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Wprowadź adres URL (np. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Wprowadź adres URL (np. http://localhost:11434/)", "Enter your current password": "", "Enter Your Email": "Wprowadź swój adres email", "Enter Your Full Name": "Wprowadź swoje imię i nazwisko", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "Wprowadź swoje hasło", "Enter your prompt": "", "Enter Your Role": "Wprowadź swoją rolę", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Błąd", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Eksperymentalne", "Explore the cosmos": "", "Export": "Eksport", "Export All Archived Chats": "", "Export All Chats (All Users)": "Eksportuj wszystkie czaty (wszyscy użytkownicy)", "Export chat (.json)": "", "Export Chats": "Eksportuj czaty", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "Eksportuj modele", "Export Presets": "", "Export Prompts": "Eksportuj prompty", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Nie udało się utworzyć klucza API.", "Failed to read clipboard contents": "Nie udało się odczytać zawartości schowka", "Failed to save models configuration": "", "Failed to update settings": "", "February": "<PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "<PERSON><PERSON> pliku", "File not found.": "Plik nie został znaleziony.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Wykryto podszywanie się pod odcisk palca: Nie można używać inicjałów jako awatara. Przechodzenie do domyślnego obrazu profilowego.", "Fluidly stream large external response chunks": "Płynnie przesyłaj strumieniowo duże fragmenty odpowiedzi zewnętrznych", "Focus chat input": "Skoncentruj na czacie", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "Kara za częstotli<PERSON>ść", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "Ogólne", "General Settings": "Ogólne ustawienia", "Generate Image": "", "Generating search query": "Generowanie zapytania", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "Do<PERSON> odpowiedź", "Google Drive": "", "Google PSE API Key": "Klucz API Google PSE", "Google PSE Engine Id": "Identyfikator silnika Google PSE", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "nie ma rozmów.", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "Pomoc", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Host": "", "How can I help you today?": "Jak mogę Ci dzisiaj pomóc?", "How would you rate this response?": "", "Hybrid Search": "Szukanie hybrydowe", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Generow<PERSON><PERSON> o<PERSON> (eksperymentalne)", "Image Generation Engine": "Silnik generowania obrazu", "Image Max Compression Size": "", "Image Settings": "Ustawi<PERSON> obrazu", "Images": "<PERSON><PERSON><PERSON>", "Import Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "Importowanie modeli", "Import Presets": "", "Import Prompts": "Import<PERSON><PERSON> prompty", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON><PERSON> flagę `--api` podczas uruchamiania stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Informacji", "Input commands": "Wprowadź komendy", "Install from Github URL": "Instalowanie z adresu URL usługi Github", "Instant Auto-Send After Voice Transcription": "", "Interface": "Interfejs", "Invalid file format.": "", "Invalid Tag": "Nieprawidłowy tag", "is typing...": "", "January": "Styczeń", "Jina API Key": "", "join our Discord for help.": "Dołącz do naszego Discorda po pomoc.", "JSON": "JSON", "JSON Preview": "JSON (wersja zapoznawcza)", "July": "Lipiec", "June": "Czerwiec", "JWT Expiration": "Wygaśnięcie JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Alive": "<PERSON><PERSON><PERSON>", "Key": "", "Keyboard shortcuts": "Skróty <PERSON>zo<PERSON>", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "Język", "Last Active": "Ostatnio aktywny", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Stworzone przez społeczność OpenWebUI", "Make sure to enclose them with": "Upewnij się, że są one zamknięte w", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Zarządzanie <PERSON>", "March": "Marzec", "Max Tokens (num_predict)": "Maksymalna liczba żetonów (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maksymalnie 3 modele można pobierać jednocześnie. Spróbuj ponownie później.", "May": "Maj", "Memories accessible by LLMs will be shown here.": "Pamięci używane przez LLM będą tutaj widoczne.", "Memory": "<PERSON><PERSON><PERSON><PERSON>", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Wiadomości wysyłane po utworzeniu linku nie będą udostępniane. Użytkownicy z adresem URL będą mogli wyświetlić udostępniony czat.", "Min P": "", "Minimum Score": "Minimalny wynik", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' został pomyślnie pobrany.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' jest już w kolejce do pobrania.", "Model {{modelId}} not found": "Model {{modelId}} nie został znaleziony", "Model {{modelName}} is not vision capable": "Model {{modelName}} nie jest w stanie <PERSON>", "Model {{name}} is now {{status}}": "Model {{name}} to teraz {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Wykryto ścieżkę systemu plików modelu. Wymagana jest krótka nazwa modelu do aktualizacji, nie można kontynuować.", "Model Filtering": "", "Model ID": "Identyfikator modelu", "Model IDs": "", "Model Name": "", "Model not selected": "Model nie został wybrany", "Model Params": "Parametry modelu", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "Z<PERSON>rt<PERSON><PERSON>ć pliku modelu", "Models": "<PERSON>e", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON><PERSON><PERSON>j", "Name": "Nazwa", "Name your knowledge base": "", "New Chat": "<PERSON><PERSON> czat", "New folder": "", "New Password": "Nowe hasło", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Nie znaleziono rezultatów", "No search query generated": "<PERSON><PERSON> w<PERSON><PERSON><PERSON> zapytania wyszukiwania", "No source available": "Źródło nie dost<PERSON>ne", "No users were found.": "", "No valves to update": "", "None": "Żaden", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Uwaga: <PERSON><PERSON><PERSON> wynik, s<PERSON><PERSON> zwróci jedynie dokumenty z wynikiem większym lub równym minimalnemu.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Powiadomienia", "November": "Listopad", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "", "October": "Październik", "Off": "Wyłączony", "Okay, Let's Go!": "Okej, z<PERSON><PERSON><PERSON>y!", "OLED Dark": "Ciemny OLED", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Interfejs API Ollama wyłączony", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "Włączony", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "W poleceniu dozwolone są tylko znaki alfanumeryczne i myślniki.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ups! Wygląda na to, że URL jest nieprawidłowy. Sprawdź jeszcze raz i spróbuj ponownie.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ups! Używasz nieobsługiwanej metody (tylko interfejs front-end). Proszę obsłużyć interfejs WebUI z poziomu backendu.", "Open in full screen": "", "Open new chat": "Otwórz nowy czat", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "Konfiguracja OpenAI API", "OpenAI API Key is required.": "Klucz API OpenAI jest wymagany.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/Klucz OpenAI jest wymagany.", "or": "lub", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "<PERSON><PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "Dokument PDF (.pdf)", "PDF Extract Images (OCR)": "PDF Wyodrę<PERSON><PERSON><PERSON> (OCR)", "pending": "oczek<PERSON><PERSON><PERSON><PERSON>", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "Odmowa dostępu do mikrofonu: {{error}}", "Permissions": "", "Personalization": "Personalizacja", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "Rurociągów", "Pipelines Not Detected": "", "Pipelines Valves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plain text (.txt)": "Zwykły tekst (.txt)", "Playground": "Plac zabaw", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Poprzednie 30 dni", "Previous 7 days": "Poprzednie 7 dni", "Profile Image": "<PERSON><PERSON><PERSON><PERSON> profilowy", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (np. powiedz mi zabawny fakt o Imperium Rzymskim", "Prompt Content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prompta", "Prompt created successfully": "", "Prompt suggestions": "<PERSON><PERSON><PERSON> prompta", "Prompt updated successfully": "", "Prompts": "Prompty", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Po<PERSON>rz \"{{searchValue}}\" z Ollama.com", "Pull a model from Ollama.com": "Pobierz model z Ollama.com", "Query Generation Prompt": "", "Query Params": "Parametry zapytania", "RAG Template": "Szablon RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "Czytaj na głos", "Record voice": "Na<PERSON>j <PERSON>ł<PERSON>", "Redirecting you to OpenWebUI Community": "Przekierowujemy Cię do społeczności OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON>", "Release Notes": "Notatki wydania", "Relevance": "", "Remove": "Usuń", "Remove Model": "Usuń model", "Rename": "ZMień nazwę", "Reorder Models": "", "Repeat Last N": "Powtórz ostatnie N", "Reply in Thread": "", "Request Mode": "Tryb żądania", "Reranking Model": "Zmiana <PERSON>u modelu", "Reranking model disabled": "Zmiana rankingu modelu zablokowana", "Reranking model set to \"{{reranking_model}}\"": "Zmiana rankingu modelu ustawiona na \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Rola", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RLT", "Run": "", "Running": "", "Save": "<PERSON><PERSON><PERSON><PERSON>", "Save & Create": "Zapisz i utwórz", "Save & Update": "Zapisz i zaktualizuj", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Bezpośrednie zapisywanie dzienników czatu w pamięci przeglądarki nie jest już obsługiwane. Prosimy o pobranie i usunięcie dzienników czatu, klikając poniższy przycisk. Nie martw się, możesz łatwo ponownie zaimportować dzienniki czatu do backendu za pomocą", "Scroll to bottom when switching between branches": "", "Search": "Szukaj", "Search a model": "Szukaj modelu", "Search Base": "", "Search Chats": "Szukaj w czatach", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "Szukaj modeli", "Search options": "", "Search Prompts": "Szukaj promptów", "Search Result Count": "Liczba wyników wyszukiwania", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "Adres URL zapytania Searxng", "See readme.md for instructions": "Zajrzyj do readme.md po instrukcje", "See what's new": "Zobacz co nowego", "Seed": "Seed", "Select a base model": "Wybieranie modelu bazowego", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "Wybierz model", "Select a pipeline": "<PERSON><PERSON><PERSON><PERSON><PERSON> potoku", "Select a pipeline url": "Wybieranie adresu URL potoku", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "Wybierz model", "Select only one model to call": "", "Selected model(s) do not support image inputs": "Wybrane modele nie obsługują danych wejściowych obrazu", "Semantic distance to query": "", "Send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Send a message": "", "Send a Message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Send message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Wrzesień", "Serper API Key": "Klucz API Serper", "Serply API Key": "", "Serpstack API Key": "Klucz API Serpstack", "Server connection verified": "Połączenie z serwerem zweryfikowane", "Set as default": "Ustaw jako <PERSON>", "Set CFG Scale": "", "Set Default Model": "Ustaw domyślny model", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Ustaw model osadzania (e.g. {{model}})", "Set Image Size": "Ustaw roz<PERSON>r obrazu", "Set reranking model (e.g. {{model}})": "Ustaw zmianę rankingu modelu (e.g. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Ustaw kroki", "Set Task Model": "Ustawianie modelu zadań", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Ustaw głos", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Ustawienia", "Settings saved successfully!": "Ustawienia zapisane pomyślnie!", "Share": "Udostępnij", "Share Chat": "Udostęp<PERSON>j <PERSON>t", "Share to OpenWebUI Community": "Dziel się z społecznością OpenWebUI", "Show": "Po<PERSON><PERSON>", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "Pokaż skróty", "Show your support!": "", "Sign in": "<PERSON><PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign up": "Zarejestruj się", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Źródło", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Błąd rozpoznawania mowy: {{error}}", "Speech-to-Text Engine": "Silnik mowy na tekst", "Stop": "", "Stop Sequence": "Zatrzym<PERSON>", "Stream Chat Response": "", "STT Model": "", "STT Settings": "Ustawienia STT", "Success": "Sukces", "Successfully updated.": "Pomyślnie zaktualizowano.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "System", "System Instructions": "", "System Prompt": "Prompt systemowy", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "Temperatura", "Template": "Szablon", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "Silnik tekstu na mowę", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Dzięki za informację zwrotną!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Wynik powinien być wartością pomiędzy 0.0 (0%) a 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "Motyw", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "To zapewnia, że Twoje cenne rozmowy są bezpiecznie zapisywane w bazie danych backendowej. Dziękujemy!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Porada: Aktualizuj wiele zmiennych kolejno, naciskając klawisz tabulatora w polu wprowadzania czatu po każdej zmianie.", "Title": "<PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Tytuł (np. Powiedz mi jakiś zabawny fakt)", "Title Auto-Generation": "Automatyczne generowanie tytułu", "Title cannot be an empty string.": "Tytuł nie może być pusty", "Title Generation Prompt": "Prompt generowania tytułu", "TLS": "", "To access the available model names for downloading,": "<PERSON><PERSON> <PERSON><PERSON><PERSON>ć dostęp do dostępnych nazw modeli do pobrania,", "To access the GGUF models available for downloading,": "<PERSON><PERSON> <PERSON><PERSON><PERSON>ć dostęp do dostępnych modeli GGUF do pobrania,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Toggle settings": "Przełącz ustawienia", "Toggle sidebar": "Przełącz panel boczny", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Najlepsze K", "Top P": "Najlepsze P", "Transformers": "", "Trouble accessing Ollama?": "Problemy z dostępem do Ollama?", "TTS Model": "", "TTS Settings": "Ustawienia TTS", "TTS Voice": "", "Type": "<PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Wprowadź adres URL do pobrania z Hugging Face", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "Uaktualnij i skopiuj link", "Update for the latest features and improvements.": "", "Update password": "Aktualiza<PERSON><PERSON> hasła", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "Prześlij model GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "Prześlij pliki", "Upload Pipeline": "", "Upload Progress": "Postęp przesyłania", "URL": "", "URL Mode": "Tryb adresu URL", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "Użyj Gravatara", "Use groups to group your users and assign permissions.": "", "Use Initials": "Użyj inicjałów", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "użytkownik", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "Użytkownicy", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "Wykorzystaj", "Valid time units:": "Poprawne jednostki czasu:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "zmienna", "variable to have them replaced with clipboard content.": "zmienna która zostanie zastąpiona zawartością schowka.", "Version": "<PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "Ostrzeżenie", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Uwaga: <PERSON><PERSON><PERSON> lub zmie<PERSON>z model o<PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON> musiał ponownie zaimportować wszystkie dokumenty.", "Web": "<PERSON><PERSON><PERSON>", "Web API": "", "Web Loader Settings": "Ustawienia pobierania z sieci", "Web Search": "Wyszukiwarka w Internecie", "Web Search Engine": "Wyszukiwarka internetowa", "Web Search Query Generation": "", "Webhook URL": "URL webhook", "WebUI Settings": "Ustawienia interfejsu WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Co nowego w", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "<PERSON><PERSON><PERSON> rob<PERSON>", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Na<PERSON>z sugestię do polecenia (np. <PERSON>?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Napisz podsumowanie w 50 słowach, kt<PERSON><PERSON> podsumowuje [temat lub słowo kluczowe].", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON><PERSON><PERSON><PERSON>", "You": "Ty", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "Nie masz zarchiwizowanych rozmów.", "You have shared this chat": "Udostępniłeś ten czat", "You're a helpful assistant.": "Jesteś pomocnym asystentem.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "Ustawienia pobierania z Youtube"}