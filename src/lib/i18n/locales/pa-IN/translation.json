{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'ਸ', 'ਮ', 'ਘੰ', 'ਦ', 'ਹਫ਼ਤਾ' ਜਾਂ '-1' ਬਿਨਾ ਮਿਆਦ ਦੇ।", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(ਉਦਾਹਰਣ ਦੇ ਤੌਰ ਤੇ `sh webui.sh --api`)", "(latest)": "(ਤਾਜ਼ਾ)", "{{ models }}": "{{ ਮਾਡਲ }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} ਦੀਆਂ ਗੱਲਾਂ", "{{webUIName}} Backend Required": "{{webUIName}} ਬੈਕਐਂਡ ਲੋੜੀਂਦਾ ਹੈ", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "ਚੈਟਾਂ ਅਤੇ ਵੈੱਬ ਖੋਜ ਪੁੱਛਗਿੱਛਾਂ ਵਾਸਤੇ ਸਿਰਲੇਖ ਤਿਆਰ ਕਰਨ ਵਰਗੇ ਕਾਰਜ ਾਂ ਨੂੰ ਕਰਦੇ ਸਮੇਂ ਇੱਕ ਕਾਰਜ ਮਾਡਲ ਦੀ ਵਰਤੋਂ ਕੀਤੀ ਜਾਂਦੀ ਹੈ", "a user": "ਇੱਕ ਉਪਭੋਗਤਾ", "About": "ਬਾਰੇ", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "ਖਾਤਾ", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "ਸ਼ਾਮਲ ਕਰੋ", "Add a model ID": "", "Add a short description about what this model does": "ਇਸ ਬਾਰੇ ਇੱਕ ਸੰਖੇਪ ਵੇਰਵਾ ਸ਼ਾਮਲ ਕਰੋ ਕਿ ਇਹ ਮਾਡਲ ਕੀ ਕਰਦਾ ਹੈ", "Add a tag": "ਇੱਕ ਟੈਗ ਸ਼ਾਮਲ ਕਰੋ", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "ਕਸਟਮ ਪ੍ਰੰਪਟ ਸ਼ਾਮਲ ਕਰੋ", "Add Files": "ਫਾਈਲਾਂ ਸ਼ਾਮਲ ਕਰੋ", "Add Group": "", "Add Memory": "ਮਿਹਾਨ ਸ਼ਾਮਲ ਕਰੋ", "Add Model": "ਮਾਡਲ ਸ਼ਾਮਲ ਕਰੋ", "Add Reaction": "", "Add Tag": "", "Add Tags": "ਟੈਗ ਸ਼ਾਮਲ ਕਰੋ", "Add text content": "", "Add User": "ਉਪਭੋਗਤਾ ਸ਼ਾਮਲ ਕਰੋ", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "ਇਹ ਸੈਟਿੰਗਾਂ ਨੂੰ ਠੀਕ ਕਰਨ ਨਾਲ ਸਾਰੇ ਉਪਭੋਗਤਾਵਾਂ ਲਈ ਬਦਲਾਅ ਲਾਗੂ ਹੋਣਗੇ।", "admin": "ਪ੍ਰਬੰਧਕ", "Admin": "", "Admin Panel": "ਪ੍ਰਬੰਧਕ ਪੈਨਲ", "Admin Settings": "ਪ੍ਰਬੰਧਕ ਸੈਟਿੰਗਾਂ", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "ਉੱਚ ਸਤਰ ਦੇ ਪੈਰਾਮੀਟਰ", "Advanced Params": "ਐਡਵਾਂਸਡ ਪਰਮਜ਼", "All Documents": "ਸਾਰੇ ਡਾਕੂਮੈਂਟ", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "ਗੱਲਬਾਤ ਮਿਟਾਉਣ ਦੀ ਆਗਿਆ ਦਿਓ", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "ਪਹਿਲਾਂ ਹੀ ਖਾਤਾ ਹੈ?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "ਇੱਕ ਸਹਾਇਕ", "and": "ਅਤੇ", "and {{COUNT}} more": "", "and create a new shared link.": "ਅਤੇ ਇੱਕ ਨਵਾਂ ਸਾਂਝਾ ਲਿੰਕ ਬਣਾਓ।", "API Base URL": "API ਬੇਸ URL", "API Key": "API ਕੁੰਜੀ", "API Key created.": "API ਕੁੰਜੀ ਬਣਾਈ ਗਈ।", "API Key Endpoint Restrictions": "", "API keys": "API ਕੁੰਜੀਆਂ", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "ਅਪ੍ਰੈਲ", "Archive": "ਆਰਕਾਈਵ", "Archive All Chats": "ਸਾਰੀਆਂ ਚੈਟਾਂ ਨੂੰ ਆਰਕਾਈਵ ਕਰੋ", "Archived Chats": "ਆਰਕਾਈਵ ਕੀਤੀਆਂ ਗੱਲਾਂ", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਹੋ?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "ਫਾਈਲ ਜੋੜੋ", "Attribute for Username": "", "Audio": "ਆਡੀਓ", "August": "ਅਗਸਤ", "Authenticate": "", "Auto-Copy Response to Clipboard": "ਜਵਾਬ ਆਟੋ ਕਾਪੀ ਕਲਿੱਪਬੋਰਡ 'ਤੇ", "Auto-playback response": "ਆਟੋ-ਪਲੇਬੈਕ ਜਵਾਬ", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 ਬੇਸ URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 ਬੇਸ URL ਦੀ ਲੋੜ ਹੈ।", "Available list": "", "available!": "ਉਪਲਬਧ ਹੈ!", "Azure AI Speech": "", "Azure Region": "", "Back": "ਵਾਪਸ", "Bad": "", "Bad Response": "ਖਰਾਬ ਜਵਾਬ", "Banners": "ਬੈਨਰ", "Base Model (From)": "ਬੇਸ ਮਾਡਲ (ਤੋਂ)", "Batch Size (num_batch)": "", "before": "ਪਹਿਲਾਂ", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "ਬਹਾਦਰ ਖੋਜ API ਕੁੰਜੀ", "By {{name}}": "", "Bypass SSL verification for Websites": "ਵੈਬਸਾਈਟਾਂ ਲਈ SSL ਪ੍ਰਮਾਣਿਕਤਾ ਨੂੰ ਬਾਈਪਾਸ ਕਰੋ", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "ਰੱਦ ਕਰੋ", "Capabilities": "ਸਮਰੱਥਾਵਾਂ", "Capture": "", "Certificate Path": "", "Change Password": "ਪਾਸਵਰਡ ਬਦਲੋ", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "ਗੱਲਬਾਤ", "Chat Background Image": "", "Chat Bubble UI": "ਗੱਲਬਾਤ ਬਬਲ UI", "Chat Controls": "", "Chat direction": "ਗੱਲਬਾਤ ਡਿਰੈਕਟਨ", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "ਗੱਲਾਂ", "Check Again": "ਮੁੜ ਜਾਂਚ ਕਰੋ", "Check for updates": "ਅੱਪਡੇਟ ਲਈ ਜਾਂਚ ਕਰੋ", "Checking for updates...": "ਅੱਪਡੇਟ ਲਈ ਜਾਂਚ ਰਿਹਾ ਹੈ...", "Choose a model before saving...": "ਸੰਭਾਲਣ ਤੋਂ ਪਹਿਲਾਂ ਇੱਕ ਮਾਡਲ ਚੁਣੋ...", "Chunk Overlap": "ਚੰਕ ਓਵਰਲੈਪ", "Chunk Params": "ਚੰਕ ਪੈਰਾਮੀਟਰ", "Chunk Size": "ਚੰਕ ਆਕਾਰ", "Ciphers": "", "Citation": "ਹਵਾਲਾ", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "ਮਦਦ ਲਈ ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ।", "Click here to": "ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "ਚੁਣਨ ਲਈ ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ", "Click here to select a csv file.": "CSV ਫਾਈਲ ਚੁਣਨ ਲਈ ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ।", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ।", "Click on the user role button to change a user's role.": "ਉਪਭੋਗਤਾ ਦੀ ਭੂਮਿਕਾ ਬਦਲਣ ਲਈ ਉਪਭੋਗਤਾ ਭੂਮਿਕਾ ਬਟਨ 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "ਕਲੋਨ", "Close": "ਬੰਦ ਕਰੋ", "Code execution": "", "Code formatted successfully": "", "Collection": "ਸੰਗ੍ਰਹਿ", "Color": "", "ComfyUI": "ਕੰਫੀਯੂਆਈ", "ComfyUI API Key": "", "ComfyUI Base URL": "ਕੰਫੀਯੂਆਈ ਬੇਸ URL", "ComfyUI Base URL is required.": "ਕੰਫੀਯੂਆਈ ਬੇਸ URL ਦੀ ਲੋੜ ਹੈ।", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "ਕਮਾਂਡ", "Completions": "", "Concurrent Requests": "ਸਮਕਾਲੀ ਬੇਨਤੀਆਂ", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "Confirm your action": "", "Confirm your new password": "", "Connections": "ਕਨੈਕਸ਼ਨ", "Contact Admin for WebUI Access": "", "Content": "ਸਮੱਗਰੀ", "Content Extraction": "", "Context Length": "ਸੰਦਰਭ ਲੰਬਾਈ", "Continue Response": "ਜਵਾਬ ਜਾਰੀ ਰੱਖੋ", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "ਸਾਂਝੇ ਕੀਤੇ ਗੱਲਬਾਤ URL ਨੂੰ ਕਲਿੱਪਬੋਰਡ 'ਤੇ ਕਾਪੀ ਕਰ ਦਿੱਤਾ!", "Copied to clipboard": "", "Copy": "ਕਾਪੀ ਕਰੋ", "Copy last code block": "ਆਖਰੀ ਕੋਡ ਬਲਾਕ ਨੂੰ ਕਾਪੀ ਕਰੋ", "Copy last response": "ਆਖਰੀ ਜਵਾਬ ਨੂੰ ਕਾਪੀ ਕਰੋ", "Copy Link": "ਲਿੰਕ ਕਾਪੀ ਕਰੋ", "Copy to clipboard": "", "Copying to clipboard was successful!": "ਕਲਿੱਪਬੋਰਡ 'ਤੇ ਕਾਪੀ ਕਰਨਾ ਸਫਲ ਰਿਹਾ!", "Create": "", "Create a knowledge base": "", "Create a model": "ਇੱਕ ਮਾਡਲ ਬਣਾਓ", "Create Account": "ਖਾਤਾ ਬਣਾਓ", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "ਨਵੀਂ ਕੁੰਜੀ ਬਣਾਓ", "Create new secret key": "ਨਵੀਂ ਗੁਪਤ ਕੁੰਜੀ ਬਣਾਓ", "Created at": "ਤੇ ਬਣਾਇਆ ਗਿਆ", "Created At": "ਤੇ ਬਣਾਇਆ ਗਿਆ", "Created by": "", "CSV Import": "", "Current Model": "ਮੌਜੂਦਾ ਮਾਡਲ", "Current Password": "ਮੌਜੂਦਾ ਪਾਸਵਰਡ", "Custom": "ਕਸਟਮ", "Dark": "ਗੂੜ੍ਹਾ", "Database": "ਡਾਟਾਬੇਸ", "December": "ਦਸੰਬਰ", "Default": "ਮੂਲ", "Default (Open AI)": "", "Default (SentenceTransformers)": "ਮੂਲ (ਸੈਂਟੈਂਸਟ੍ਰਾਂਸਫਾਰਮਰਸ)", "Default Model": "ਡਿਫਾਲਟ ਮਾਡਲ", "Default model updated": "ਮੂਲ ਮਾਡਲ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "ਮੂਲ ਪ੍ਰੰਪਟ ਸੁਝਾਅ", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "ਮੂਲ ਉਪਭੋਗਤਾ ਭੂਮਿਕਾ", "Delete": "ਮਿਟਾਓ", "Delete a model": "ਇੱਕ ਮਾਡਲ ਮਿਟਾਓ", "Delete All Chats": "ਸਾਰੀਆਂ ਚੈਟਾਂ ਨੂੰ ਮਿਟਾਓ", "Delete All Models": "", "Delete chat": "ਗੱਲਬਾਤ ਮਿਟਾਓ", "Delete Chat": "ਗੱਲਬਾਤ ਮਿਟਾਓ", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "ਇਸ ਲਿੰਕ ਨੂੰ ਮਿਟਾਓ", "Delete tool?": "", "Delete User": "ਉਪਭੋਗਤਾ ਮਿਟਾਓ", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} ਮਿਟਾਇਆ ਗਿਆ", "Deleted {{name}}": "ਮਿਟਾ ਦਿੱਤਾ ਗਿਆ {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "ਵਰਣਨਾ", "Disabled": "", "Discover a function": "", "Discover a model": "ਇੱਕ ਮਾਡਲ ਲੱਭੋ", "Discover a prompt": "ਇੱਕ ਪ੍ਰੰਪਟ ਖੋਜੋ", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "ਕਸਟਮ ਪ੍ਰੰਪਟਾਂ ਨੂੰ ਖੋਜੋ, ਡਾਊਨਲੋਡ ਕਰੋ ਅਤੇ ਪੜਚੋਲ ਕਰੋ", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "ਮਾਡਲ ਪ੍ਰੀਸੈਟਾਂ ਨੂੰ ਖੋਜੋ, ਡਾਊਨਲੋਡ ਕਰੋ ਅਤੇ ਪੜਚੋਲ ਕਰੋ", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "ਗੱਲਬਾਤ 'ਚ ਤੁਹਾਡੇ ਸਥਾਨ 'ਤੇ ਉਪਭੋਗਤਾ ਨਾਮ ਦਿਖਾਓ", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "ਡਾਕੂਮੈਂਟ", "Documentation": "", "Documents": "ਡਾਕੂਮੈਂਟ", "does not make any external connections, and your data stays securely on your locally hosted server.": "ਕੋਈ ਬਾਹਰੀ ਕਨੈਕਸ਼ਨ ਨਹੀਂ ਬਣਾਉਂਦਾ, ਅਤੇ ਤੁਹਾਡਾ ਡਾਟਾ ਤੁਹਾਡੇ ਸਥਾਨਕ ਸਰਵਰ 'ਤੇ ਸੁਰੱਖਿਅਤ ਰਹਿੰਦਾ ਹੈ।", "Don't have an account?": "ਖਾਤਾ ਨਹੀਂ ਹੈ?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "ਡਾਊਨਲੋਡ", "Download canceled": "ਡਾਊਨਲੋਡ ਰੱਦ ਕੀਤਾ ਗਿਆ", "Download Database": "ਡਾਟਾਬੇਸ ਡਾਊਨਲੋਡ ਕਰੋ", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "ਗੱਲਬਾਤ ਵਿੱਚ ਸ਼ਾਮਲ ਕਰਨ ਲਈ ਕੋਈ ਵੀ ਫਾਈਲ ਇੱਥੇ ਛੱਡੋ", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "ਉਦਾਹਰਣ ਲਈ '30ਸ','10ਮਿ'. ਸਹੀ ਸਮਾਂ ਇਕਾਈਆਂ ਹਨ 'ਸ', 'ਮ', 'ਘੰ'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "ਸੰਪਾਦਨ ਕਰੋ", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "ਉਪਭੋਗਤਾ ਸੰਪਾਦਨ ਕਰੋ", "Edit User Group": "", "ElevenLabs": "", "Email": "ਈਮੇਲ", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ", "Embedding Model Engine": "ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ ਇੰਜਣ", "Embedding model set to \"{{embedding_model}}\"": "ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ ਨੂੰ \"{{embedding_model}}\" 'ਤੇ ਸੈੱਟ ਕੀਤਾ ਗਿਆ", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "ਕਮਿਊਨਿਟੀ ਸ਼ੇਅਰਿੰਗ ਨੂੰ ਸਮਰੱਥ ਕਰੋ", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "ਨਵੇਂ ਸਾਈਨ ਅਪ ਯੋਗ ਕਰੋ", "Enable Web Search": "ਵੈੱਬ ਖੋਜ ਨੂੰ ਸਮਰੱਥ ਕਰੋ", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "ਸੁਨਿਸ਼ਚਿਤ ਕਰੋ ਕਿ ਤੁਹਾਡੀ CSV ਫਾਈਲ ਵਿੱਚ ਇਸ ਕ੍ਰਮ ਵਿੱਚ 4 ਕਾਲਮ ਹਨ: ਨਾਮ, ਈਮੇਲ, ਪਾਸਵਰਡ, ਭੂਮਿਕਾ।", "Enter {{role}} message here": "{{role}} ਸੁਨੇਹਾ ਇੱਥੇ ਦਰਜ ਕਰੋ", "Enter a detail about yourself for your LLMs to recall": "ਤੁਹਾਡੇ LLMs ਨੂੰ ਸੁਨੇਹਾ ਕਰਨ ਲਈ ਸੁਨੇਹਾ ਇੱਥੇ ਦਰਜ ਕਰੋ", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "ਬਹਾਦਰ ਖੋਜ API ਕੁੰਜੀ ਦਾਖਲ ਕਰੋ", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "ਚੰਕ ਓਵਰਲੈਪ ਦਰਜ ਕਰੋ", "Enter Chunk Size": "ਚੰਕ ਆਕਾਰ ਦਰਜ ਕਰੋ", "Enter description": "", "Enter Github Raw URL": "Github ਕੱਚਾ URL ਦਾਖਲ ਕਰੋ", "Enter Google PSE API Key": "Google PSE API ਕੁੰਜੀ ਦਾਖਲ ਕਰੋ", "Enter Google PSE Engine Id": "Google PSE ਇੰਜਣ ID ਦਾਖਲ ਕਰੋ", "Enter Image Size (e.g. 512x512)": "ਚਿੱਤਰ ਆਕਾਰ ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "ਭਾਸ਼ਾ ਕੋਡ ਦਰਜ ਕਰੋ", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "ਮਾਡਲ ਟੈਗ ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "ਕਦਮਾਂ ਦੀ ਗਿਣਤੀ ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "ਸਕੋਰ ਦਰਜ ਕਰੋ", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Searxng Query URL ਦਾਖਲ ਕਰੋ", "Enter Seed": "", "Enter Serper API Key": "Serper API ਕੁੰਜੀ ਦਾਖਲ ਕਰੋ", "Enter Serply API Key": "", "Enter Serpstack API Key": "Serpstack API ਕੁੰਜੀ ਦਾਖਲ ਕਰੋ", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "ਰੋਕਣ ਦਾ ਕ੍ਰਮ ਦਰਜ ਕਰੋ", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "ਸਿਖਰ K ਦਰਜ ਕਰੋ", "Enter URL (e.g. http://127.0.0.1:7860/)": "URL ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "ਆਪਣੀ ਈਮੇਲ ਦਰਜ ਕਰੋ", "Enter Your Full Name": "ਆਪਣਾ ਪੂਰਾ ਨਾਮ ਦਰਜ ਕਰੋ", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "ਆਪਣਾ ਪਾਸਵਰਡ ਦਰਜ ਕਰੋ", "Enter your prompt": "", "Enter Your Role": "ਆਪਣੀ ਭੂਮਿਕਾ ਦਰਜ ਕਰੋ", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "ਗਲਤੀ", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "ਪਰਮਾਣੂਕ੍ਰਿਤ", "Explore the cosmos": "", "Export": "ਨਿਰਯਾਤ", "Export All Archived Chats": "", "Export All Chats (All Users)": "ਸਾਰੀਆਂ ਗੱਲਾਂ ਨਿਰਯਾਤ ਕਰੋ (ਸਾਰੇ ਉਪਭੋਗਤਾ)", "Export chat (.json)": "", "Export Chats": "ਗੱਲਾਂ ਨਿਰਯਾਤ ਕਰੋ", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "ਨਿਰਯਾਤ ਮਾਡਲ", "Export Presets": "", "Export Prompts": "ਪ੍ਰੰਪਟ ਨਿਰਯਾਤ ਕਰੋ", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "API ਕੁੰਜੀ ਬਣਾਉਣ ਵਿੱਚ ਅਸਫਲ।", "Failed to read clipboard contents": "ਕਲਿੱਪਬੋਰਡ ਸਮੱਗਰੀ ਪੜ੍ਹਣ ਵਿੱਚ ਅਸਫਲ", "Failed to save models configuration": "", "Failed to update settings": "", "February": "ਫਰਵਰੀ", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "ਫਾਈਲ ਮੋਡ", "File not found.": "ਫਾਈਲ ਨਹੀਂ ਮਿਲੀ।", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "ਫਿੰਗਰਪ੍ਰਿੰਟ ਸਪੂਫਿੰਗ ਪਾਈ ਗਈ: ਅਵਤਾਰ ਵਜੋਂ ਸ਼ੁਰੂਆਤੀ ਅੱਖਰ ਵਰਤਣ ਵਿੱਚ ਅਸਮਰੱਥ। ਮੂਲ ਪ੍ਰੋਫਾਈਲ ਚਿੱਤਰ 'ਤੇ ਡਿਫਾਲਟ।", "Fluidly stream large external response chunks": "ਵੱਡੇ ਬਾਹਰੀ ਜਵਾਬ ਚੰਕਾਂ ਨੂੰ ਸਹੀ ਢੰਗ ਨਾਲ ਸਟ੍ਰੀਮ ਕਰੋ", "Focus chat input": "ਗੱਲਬਾਤ ਇਨਪੁਟ 'ਤੇ ਧਿਆਨ ਦਿਓ", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "ਬਾਰੰਬਾਰਤਾ ਜੁਰਮਾਨਾ", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "ਆਮ", "General Settings": "ਆਮ ਸੈਟਿੰਗਾਂ", "Generate Image": "", "Generating search query": "ਖੋਜ ਪੁੱਛਗਿੱਛ ਤਿਆਰ ਕਰਨਾ", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "ਵਧੀਆ ਜਵਾਬ", "Google Drive": "", "Google PSE API Key": "Google PSE API ਕੁੰਜੀ", "Google PSE Engine Id": "ਗੂਗਲ PSE ਇੰਜਣ ID", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "ਹ:ਮਿੰਟ ਪੂਃ", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "ਕੋਈ ਗੱਲਬਾਤ ਨਹੀਂ ਹੈ।", "Hello, {{name}}": "ਸਤ ਸ੍ਰੀ ਅਕਾਲ, {{name}}", "Help": "ਮਦਦ", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "ਲੁਕਾਓ", "Host": "", "How can I help you today?": "ਮੈਂ ਅੱਜ ਤੁਹਾਡੀ ਕਿਵੇਂ ਮਦਦ ਕਰ ਸਕਦਾ ਹਾਂ?", "How would you rate this response?": "", "Hybrid Search": "ਹਾਈਬ੍ਰਿਡ ਖੋਜ", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "ਚਿੱਤਰ ਜਨਰੇਸ਼ਨ (ਪਰਮਾਣੂਕ੍ਰਿਤ)", "Image Generation Engine": "ਚਿੱਤਰ ਜਨਰੇਸ਼ਨ ਇੰਜਣ", "Image Max Compression Size": "", "Image Settings": "ਚਿੱਤਰ ਸੈਟਿੰਗਾਂ", "Images": "ਚਿੱਤਰ", "Import Chats": "ਗੱਲਾਂ ਆਯਾਤ ਕਰੋ", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "ਮਾਡਲ ਆਯਾਤ ਕਰੋ", "Import Presets": "", "Import Prompts": "ਪ੍ਰੰਪਟ ਆਯਾਤ ਕਰੋ", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "ਸਟੇਬਲ-ਡਿਫਿਊਸ਼ਨ-ਵੈਬਯੂਆਈ ਚਲਾਉਣ ਸਮੇਂ `--api` ਝੰਡਾ ਸ਼ਾਮਲ ਕਰੋ", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "ਜਾਣਕਾਰੀ", "Input commands": "ਇਨਪੁਟ ਕਮਾਂਡਾਂ", "Install from Github URL": "Github URL ਤੋਂ ਇੰਸਟਾਲ ਕਰੋ", "Instant Auto-Send After Voice Transcription": "", "Interface": "ਇੰਟਰਫੇਸ", "Invalid file format.": "", "Invalid Tag": "ਗਲਤ ਟੈਗ", "is typing...": "", "January": "ਜਨਵਰੀ", "Jina API Key": "", "join our Discord for help.": "ਮਦਦ ਲਈ ਸਾਡੇ ਡਿਸਕੋਰਡ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ।", "JSON": "JSON", "JSON Preview": "JSON ਪੂਰਵ-ਦਰਸ਼ਨ", "July": "ਜੁਲਾਈ", "June": "ਜੂਨ", "JWT Expiration": "JWT ਮਿਆਦ ਖਤਮ", "JWT Token": "JWT ਟੋਕਨ", "Kagi Search API Key": "", "Keep Alive": "ਜੀਵਿਤ ਰੱਖੋ", "Key": "", "Keyboard shortcuts": "ਕੀਬੋਰਡ ਸ਼ਾਰਟਕਟ", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "ਭਾਸ਼ਾ", "Last Active": "ਆਖਰੀ ਸਰਗਰਮ", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "ਹਲਕਾ", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "ਓਪਨਵੈਬਯੂਆਈ ਕਮਿਊਨਿਟੀ ਦੁਆਰਾ ਬਣਾਇਆ ਗਿਆ", "Make sure to enclose them with": "ਸੁਨਿਸ਼ਚਿਤ ਕਰੋ ਕਿ ਉਨ੍ਹਾਂ ਨੂੰ ਘੇਰੋ", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "ਪਾਈਪਲਾਈਨਾਂ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰੋ", "March": "ਮਾਰਚ", "Max Tokens (num_predict)": "ਮੈਕਸ ਟੋਕਨ (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "ਇੱਕ ਸਮੇਂ ਵਿੱਚ ਵੱਧ ਤੋਂ ਵੱਧ 3 ਮਾਡਲ ਡਾਊਨਲੋਡ ਕੀਤੇ ਜਾ ਸਕਦੇ ਹਨ। ਕਿਰਪਾ ਕਰਕੇ ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "May": "ਮਈ", "Memories accessible by LLMs will be shown here.": "LLMs ਲਈ ਸਮਰੱਥ ਕਾਰਨ ਇੱਕ ਸੂਚਨਾ ਨੂੰ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ ਹੈ।", "Memory": "ਮੀਮਰ", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "ਤੁਹਾਡਾ ਲਿੰਕ ਬਣਾਉਣ ਤੋਂ ਬਾਅਦ ਤੁਹਾਡੇ ਵੱਲੋਂ ਭੇਜੇ ਗਏ ਸੁਨੇਹੇ ਸਾਂਝੇ ਨਹੀਂ ਕੀਤੇ ਜਾਣਗੇ। URL ਵਾਲੇ ਉਪਭੋਗਤਾ ਸਾਂਝੀ ਚੈਟ ਨੂੰ ਵੇਖ ਸਕਣਗੇ।", "Min P": "", "Minimum Score": "ਘੱਟੋ-ਘੱਟ ਸਕੋਰ", "Mirostat": "ਮਿਰੋਸਟੈਟ", "Mirostat Eta": "ਮਿਰੋਸਟੈਟ ਈਟਾ", "Mirostat Tau": "ਮਿਰੋਸਟੈਟ ਟਾਉ", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "ਮਾਡਲ '{{modelName}}' ਸਫਲਤਾਪੂਰਵਕ ਡਾਊਨਲੋਡ ਕੀਤਾ ਗਿਆ ਹੈ।", "Model '{{modelTag}}' is already in queue for downloading.": "ਮਾਡਲ '{{modelTag}}' ਪਹਿਲਾਂ ਹੀ ਡਾਊਨਲੋਡ ਲਈ ਕਤਾਰ ਵਿੱਚ ਹੈ।", "Model {{modelId}} not found": "ਮਾਡਲ {{modelId}} ਨਹੀਂ ਮਿਲਿਆ", "Model {{modelName}} is not vision capable": "ਮਾਡਲ {{modelName}} ਦ੍ਰਿਸ਼ਟੀ ਸਮਰੱਥ ਨਹੀਂ ਹੈ", "Model {{name}} is now {{status}}": "ਮਾਡਲ {{name}} ਹੁਣ {{status}} ਹੈ", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "ਮਾਡਲ ਫਾਈਲਸਿਸਟਮ ਪੱਥ ਪਾਇਆ ਗਿਆ। ਅੱਪਡੇਟ ਲਈ ਮਾਡਲ ਸ਼ੌਰਟਨੇਮ ਦੀ ਲੋੜ ਹੈ, ਜਾਰੀ ਨਹੀਂ ਰੱਖ ਸਕਦੇ।", "Model Filtering": "", "Model ID": "ਮਾਡਲ ID", "Model IDs": "", "Model Name": "", "Model not selected": "ਮਾਡਲ ਚੁਣਿਆ ਨਹੀਂ ਗਿਆ", "Model Params": "ਮਾਡਲ ਪਰਮਜ਼", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "ਮਾਡਲਫਾਈਲ ਸਮੱਗਰੀ", "Models": "ਮਾਡਲ", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "ਹੋਰ", "Name": "ਨਾਮ", "Name your knowledge base": "", "New Chat": "ਨਵੀਂ ਗੱਲਬਾਤ", "New folder": "", "New Password": "ਨਵਾਂ ਪਾਸਵਰਡ", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "ਕੋਈ ਨਤੀਜੇ ਨਹੀਂ ਮਿਲੇ", "No search query generated": "ਕੋਈ ਖੋਜ ਪੁੱਛਗਿੱਛ ਤਿਆਰ ਨਹੀਂ ਕੀਤੀ ਗਈ", "No source available": "ਕੋਈ ਸਰੋਤ ਉਪਲਬਧ ਨਹੀਂ", "No users were found.": "", "No valves to update": "", "None": "ਕੋਈ ਨਹੀਂ", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "ਨੋਟ: ਜੇ ਤੁਸੀਂ ਘੱਟੋ-ਘੱਟ ਸਕੋਰ ਸੈੱਟ ਕਰਦੇ ਹੋ, ਤਾਂ ਖੋਜ ਸਿਰਫ਼ ਉਹੀ ਡਾਕੂਮੈਂਟ ਵਾਪਸ ਕਰੇਗੀ ਜਿਨ੍ਹਾਂ ਦਾ ਸਕੋਰ ਘੱਟੋ-ਘੱਟ ਸਕੋਰ ਦੇ ਬਰਾਬਰ ਜਾਂ ਵੱਧ ਹੋਵੇ।", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "ਸੂਚਨਾਵਾਂ", "November": "ਨਵੰਬਰ", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (ਓਲਾਮਾ)", "OAuth ID": "", "October": "ਅਕਤੂਬਰ", "Off": "ਬੰਦ", "Okay, Let's Go!": "ਠੀਕ ਹੈ, ਚੱਲੋ ਚੱਲੀਏ!", "OLED Dark": "OLED ਗੂੜ੍ਹਾ", "Ollama": "ਓਲਾਮਾ", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API ਅਸਮਰੱਥ", "Ollama API settings updated": "", "Ollama Version": "ਓਲਾਮਾ ਵਰਜਨ", "On": "ਚਾਲੂ", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "ਕਮਾਂਡ ਸਤਰ ਵਿੱਚ ਸਿਰਫ਼ ਅਲਫ਼ਾਨਯੂਮੈਰਿਕ ਅੱਖਰ ਅਤੇ ਹਾਈਫਨ ਦੀ ਆਗਿਆ ਹੈ।", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "ਓਹੋ! ਲੱਗਦਾ ਹੈ ਕਿ URL ਗਲਤ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਜਾਂਚ ਕਰੋ ਅਤੇ ਮੁੜ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "ਓਹੋ! ਤੁਸੀਂ ਇੱਕ ਅਣਸਮਰਥਿਤ ਢੰਗ ਵਰਤ ਰਹੇ ਹੋ (ਸਿਰਫ਼ ਫਰੰਟਐਂਡ)। ਕਿਰਪਾ ਕਰਕੇ ਵੈਬਯੂਆਈ ਨੂੰ ਬੈਕਐਂਡ ਤੋਂ ਸਰਵ ਕਰੋ।", "Open in full screen": "", "Open new chat": "ਨਵੀਂ ਗੱਲਬਾਤ ਖੋਲ੍ਹੋ", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "ਓਪਨਏਆਈ", "OpenAI API": "ਓਪਨਏਆਈ API", "OpenAI API Config": "ਓਪਨਏਆਈ API ਕਨਫਿਗ", "OpenAI API Key is required.": "ਓਪਨਏਆਈ API ਕੁੰਜੀ ਦੀ ਲੋੜ ਹੈ।", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "ਓਪਨਏਆਈ URL/ਕੁੰਜੀ ਦੀ ਲੋੜ ਹੈ।", "or": "ਜਾਂ", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "ਪਾਸਵਰਡ", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF ਡਾਕੂਮੈਂਟ (.pdf)", "PDF Extract Images (OCR)": "PDF ਚਿੱਤਰ ਕੱਢੋ (OCR)", "pending": "ਬਕਾਇਆ", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "ਮਾਈਕ੍ਰੋਫ਼ੋਨ ਤੱਕ ਪਹੁੰਚਣ ਸਮੇਂ ਆਗਿਆ ਰੱਦ ਕੀਤੀ ਗਈ: {{error}}", "Permissions": "", "Personalization": "ਪਰਸੋਨਲਿਸ਼ਮ", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "ਪਾਈਪਲਾਈਨਾਂ", "Pipelines Not Detected": "", "Pipelines Valves": "ਪਾਈਪਲਾਈਨਾਂ ਵਾਲਵ", "Plain text (.txt)": "ਸਧਾਰਨ ਪਾਠ (.txt)", "Playground": "ਖੇਡ ਦਾ ਮੈਦਾਨ", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "ਪਿਛਲੇ 30 ਦਿਨ", "Previous 7 days": "ਪਿਛਲੇ 7 ਦਿਨ", "Profile Image": "ਪ੍ਰੋਫਾਈਲ ਚਿੱਤਰ", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "ਪ੍ਰੰਪਟ (ਉਦਾਹਰਣ ਲਈ ਮੈਨੂੰ ਰੋਮਨ ਸਾਮਰਾਜ ਬਾਰੇ ਇੱਕ ਮਜ਼ੇਦਾਰ ਤੱਥ ਦੱਸੋ)", "Prompt Content": "ਪ੍ਰੰਪਟ ਸਮੱਗਰੀ", "Prompt created successfully": "", "Prompt suggestions": "ਪ੍ਰੰਪਟ ਸੁਝਾਅ", "Prompt updated successfully": "", "Prompts": "ਪ੍ਰੰਪਟ", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "ਓਲਾਮਾ.ਕਾਮ ਤੋਂ \"{{searchValue}}\" ਖਿੱਚੋ", "Pull a model from Ollama.com": "ਓਲਾਮਾ.ਕਾਮ ਤੋਂ ਇੱਕ ਮਾਡਲ ਖਿੱਚੋ", "Query Generation Prompt": "", "Query Params": "ਪ੍ਰਸ਼ਨ ਪੈਰਾਮੀਟਰ", "RAG Template": "RAG ਟੈਮਪਲੇਟ", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "ਜੋਰ ਨਾਲ ਪੜ੍ਹੋ", "Record voice": "ਆਵਾਜ਼ ਰਿਕਾਰਡ ਕਰੋ", "Redirecting you to OpenWebUI Community": "ਤੁਹਾਨੂੰ ਓਪਨਵੈਬਯੂਆਈ ਕਮਿਊਨਿਟੀ ਵੱਲ ਰੀਡਾਇਰੈਕਟ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "ਮੁੜ ਬਣਾਓ", "Release Notes": "ਰਿਲੀਜ਼ ਨੋਟਸ", "Relevance": "", "Remove": "ਹਟਾਓ", "Remove Model": "ਮਾਡਲ ਹਟਾਓ", "Rename": "ਨਾਮ ਬਦਲੋ", "Reorder Models": "", "Repeat Last N": "ਆਖਰੀ N ਨੂੰ ਦੁਹਰਾਓ", "Reply in Thread": "", "Request Mode": "ਬੇਨਤੀ ਮੋਡ", "Reranking Model": "ਮਾਡਲ ਮੁੜ ਰੈਂਕਿੰਗ", "Reranking model disabled": "ਮਾਡਲ ਮੁੜ ਰੈਂਕਿੰਗ ਅਯੋਗ ਕੀਤਾ ਗਿਆ", "Reranking model set to \"{{reranking_model}}\"": "ਮਾਡਲ ਮੁੜ ਰੈਂਕਿੰਗ ਨੂੰ \"{{reranking_model}}\" 'ਤੇ ਸੈੱਟ ਕੀਤਾ ਗਿਆ", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "ਭੂਮਿਕਾ", "Rosé Pine": "ਰੋਜ਼ ਪਾਈਨ", "Rosé Pine Dawn": "ਰੋਜ਼ ਪਾਈਨ ਡਾਨ", "RTL": "RTL", "Run": "", "Running": "", "Save": "ਸੰਭਾਲੋ", "Save & Create": "ਸੰਭਾਲੋ ਅਤੇ ਬਣਾਓ", "Save & Update": "ਸੰਭਾਲੋ ਅਤੇ ਅੱਪਡੇਟ ਕਰੋ", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "ਤੁਹਾਡੇ ਬ੍ਰਾਊਜ਼ਰ ਦੇ ਸਟੋਰੇਜ ਵਿੱਚ ਸਿੱਧੇ ਗੱਲਬਾਤ ਲੌਗ ਸੰਭਾਲਣਾ ਹੁਣ ਸਮਰਥਿਤ ਨਹੀਂ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਹੇਠਾਂ ਦਿੱਤੇ ਬਟਨ 'ਤੇ ਕਲਿੱਕ ਕਰਕੇ ਆਪਣੇ ਗੱਲਬਾਤ ਲੌਗ ਡਾਊਨਲੋਡ ਅਤੇ ਮਿਟਾਉਣ ਲਈ ਕੁਝ ਸਮਾਂ ਲਓ। ਚਿੰਤਾ ਨਾ ਕਰੋ, ਤੁਸੀਂ ਆਪਣੇ ਗੱਲਬਾਤ ਲੌਗ ਨੂੰ ਬੈਕਐਂਡ ਵਿੱਚ ਆਸਾਨੀ ਨਾਲ ਮੁੜ ਆਯਾਤ ਕਰ ਸਕਦੇ ਹੋ", "Scroll to bottom when switching between branches": "", "Search": "ਖੋਜ", "Search a model": "ਇੱਕ ਮਾਡਲ ਖੋਜੋ", "Search Base": "", "Search Chats": "ਖੋਜ ਚੈਟਾਂ", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "ਖੋਜ ਮਾਡਲ", "Search options": "", "Search Prompts": "ਪ੍ਰੰਪਟ ਖੋਜੋ", "Search Result Count": "ਖੋਜ ਨਤੀਜੇ ਦੀ ਗਿਣਤੀ", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "ਹਦਾਇਤਾਂ ਲਈ readme.md ਵੇਖੋ", "See what's new": "ਨਵਾਂ ਕੀ ਹੈ ਵੇਖੋ", "Seed": "ਬੀਜ", "Select a base model": "ਆਧਾਰ ਮਾਡਲ ਚੁਣੋ", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "ਇੱਕ ਮਾਡਲ ਚੁਣੋ", "Select a pipeline": "ਪਾਈਪਲਾਈਨ ਚੁਣੋ", "Select a pipeline url": "ਪਾਈਪਲਾਈਨ URL ਚੁਣੋ", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "ਮਾਡਲ ਚੁਣੋ", "Select only one model to call": "", "Selected model(s) do not support image inputs": "ਚੁਣੇ ਗਏ ਮਾਡਲ(ਆਂ) ਚਿੱਤਰ ਇਨਪੁੱਟਾਂ ਦਾ ਸਮਰਥਨ ਨਹੀਂ ਕਰਦੇ", "Semantic distance to query": "", "Send": "ਭੇਜੋ", "Send a message": "", "Send a Message": "ਇੱਕ ਸੁਨੇਹਾ ਭੇਜੋ", "Send message": "ਸੁਨੇਹਾ ਭੇਜੋ", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "ਸਤੰਬਰ", "Serper API Key": "Serper API ਕੁੰਜੀ", "Serply API Key": "", "Serpstack API Key": "Serpstack API ਕੁੰਜੀ", "Server connection verified": "ਸਰਵਰ ਕਨੈਕਸ਼ਨ ਦੀ ਪੁਸ਼ਟੀ ਕੀਤੀ ਗਈ", "Set as default": "ਮੂਲ ਵਜੋਂ ਸੈੱਟ ਕਰੋ", "Set CFG Scale": "", "Set Default Model": "ਮੂਲ ਮਾਡਲ ਸੈੱਟ ਕਰੋ", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ ਸੈੱਟ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ {{model}})", "Set Image Size": "ਚਿੱਤਰ ਆਕਾਰ ਸੈੱਟ ਕਰੋ", "Set reranking model (e.g. {{model}})": "ਮੁੜ ਰੈਂਕਿੰਗ ਮਾਡਲ ਸੈੱਟ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "ਕਦਮ ਸੈੱਟ ਕਰੋ", "Set Task Model": "ਟਾਸਕ ਮਾਡਲ ਸੈੱਟ ਕਰੋ", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "ਆਵਾਜ਼ ਸੈੱਟ ਕਰੋ", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "ਸੈਟਿੰਗਾਂ", "Settings saved successfully!": "ਸੈਟਿੰਗਾਂ ਸਫਲਤਾਪੂਰਵਕ ਸੰਭਾਲੀਆਂ ਗਈਆਂ!", "Share": "ਸਾਂਝਾ ਕਰੋ", "Share Chat": "ਗੱਲਬਾਤ ਸਾਂਝੀ ਕਰੋ", "Share to OpenWebUI Community": "ਓਪਨਵੈਬਯੂਆਈ ਕਮਿਊਨਿਟੀ ਨਾਲ ਸਾਂਝਾ ਕਰੋ", "Show": "ਦਿਖਾਓ", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "ਸ਼ਾਰਟਕਟ ਦਿਖਾਓ", "Show your support!": "", "Sign in": "ਸਾਈਨ ਇਨ ਕਰੋ", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "ਸਾਈਨ ਆਊਟ ਕਰੋ", "Sign up": "ਰਜਿਸਟਰ ਕਰੋ", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "ਸਰੋਤ", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "ਬੋਲ ਪਛਾਣ ਗਲਤੀ: {{error}}", "Speech-to-Text Engine": "ਬੋਲ-ਤੋਂ-ਪਾਠ ਇੰਜਣ", "Stop": "", "Stop Sequence": "ਰੋਕੋ ਕ੍ਰਮ", "Stream Chat Response": "", "STT Model": "", "STT Settings": "STT ਸੈਟਿੰਗਾਂ", "Success": "ਸਫਲਤਾ", "Successfully updated.": "ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ।", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "ਸਿਸਟਮ", "System Instructions": "", "System Prompt": "ਸਿਸਟਮ ਪ੍ਰੰਪਟ", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "ਤਾਪਮਾਨ", "Template": "ਟੈਮਪਲੇਟ", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "ਪਾਠ-ਤੋਂ-ਬੋਲ ਇੰਜਣ", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "ਤੁਹਾਡੇ ਫੀਡਬੈਕ ਲਈ ਧੰਨਵਾਦ!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "ਸਕੋਰ 0.0 (0%) ਅਤੇ 1.0 (100%) ਦੇ ਵਿਚਕਾਰ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ।", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "ਥੀਮ", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "ਇਹ ਯਕੀਨੀ ਬਣਾਉਂਦਾ ਹੈ ਕਿ ਤੁਹਾਡੀਆਂ ਕੀਮਤੀ ਗੱਲਾਂ ਤੁਹਾਡੇ ਬੈਕਐਂਡ ਡਾਟਾਬੇਸ ਵਿੱਚ ਸੁਰੱਖਿਅਤ ਤੌਰ 'ਤੇ ਸੰਭਾਲੀਆਂ ਗਈਆਂ ਹਨ। ਧੰਨਵਾਦ!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "ਸਲਾਹ: ਹਰ ਬਦਲਾਅ ਦੇ ਬਾਅਦ ਗੱਲਬਾਤ ਇਨਪੁਟ ਵਿੱਚ ਟੈਬ ਕੀ ਦਬਾ ਕੇ ਲਗਾਤਾਰ ਕਈ ਵੈਰੀਏਬਲ ਸਲਾਟਾਂ ਨੂੰ ਅੱਪਡੇਟ ਕਰੋ।", "Title": "ਸਿਰਲੇਖ", "Title (e.g. Tell me a fun fact)": "ਸਿਰਲੇਖ (ਉਦਾਹਰਣ ਲਈ ਮੈਨੂੰ ਇੱਕ ਮਜ਼ੇਦਾਰ ਤੱਥ ਦੱਸੋ)", "Title Auto-Generation": "ਸਿਰਲੇਖ ਆਟੋ-ਜਨਰੇਸ਼ਨ", "Title cannot be an empty string.": "ਸਿਰਲੇਖ ਖਾਲੀ ਸਤਰ ਨਹੀਂ ਹੋ ਸਕਦਾ।", "Title Generation Prompt": "ਸਿਰਲੇਖ ਜਨਰੇਸ਼ਨ ਪ੍ਰੰਪਟ", "TLS": "", "To access the available model names for downloading,": "ਡਾਊਨਲੋਡ ਕਰਨ ਲਈ ਉਪਲਬਧ ਮਾਡਲ ਨਾਮਾਂ ਤੱਕ ਪਹੁੰਚਣ ਲਈ,", "To access the GGUF models available for downloading,": "ਡਾਊਨਲੋਡ ਕਰਨ ਲਈ ਉਪਲਬਧ GGUF ਮਾਡਲਾਂ ਤੱਕ ਪਹੁੰਚਣ ਲਈ,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "ਅੱਜ", "Toggle settings": "ਸੈਟਿੰਗਾਂ ਟੌਗਲ ਕਰੋ", "Toggle sidebar": "ਸਾਈਡਬਾਰ ਟੌਗਲ ਕਰੋ", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "ਸਿਖਰ K", "Top P": "ਸਿਖਰ P", "Transformers": "", "Trouble accessing Ollama?": "ਓਲਾਮਾ ਤੱਕ ਪਹੁੰਚਣ ਵਿੱਚ ਮੁਸ਼ਕਲ?", "TTS Model": "", "TTS Settings": "TTS ਸੈਟਿੰਗਾਂ", "TTS Voice": "", "Type": "ਕਿਸਮ", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (ਡਾਊਨਲੋਡ) URL ਟਾਈਪ ਕਰੋ", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "ਅੱਪਡੇਟ ਕਰੋ ਅਤੇ ਲਿੰਕ ਕਾਪੀ ਕਰੋ", "Update for the latest features and improvements.": "", "Update password": "ਪਾਸਵਰਡ ਅੱਪਡੇਟ ਕਰੋ", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "ਇੱਕ GGUF ਮਾਡਲ ਅਪਲੋਡ ਕਰੋ", "Upload directory": "", "Upload files": "", "Upload Files": "ਫਾਇਲਾਂ ਅੱਪਲੋਡ ਕਰੋ", "Upload Pipeline": "", "Upload Progress": "ਅਪਲੋਡ ਪ੍ਰਗਤੀ", "URL": "", "URL Mode": "URL ਮੋਡ", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "ਗ੍ਰਾਵਾਟਾਰ ਵਰਤੋ", "Use groups to group your users and assign permissions.": "", "Use Initials": "ਸ਼ੁਰੂਆਤੀ ਅੱਖਰ ਵਰਤੋ", "use_mlock (Ollama)": "use_mlock (ਓਲਾਮਾ)", "use_mmap (Ollama)": "use_mmap (ਓਲਾਮਾ)", "user": "ਉਪਭੋਗਤਾ", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "ਉਪਭੋਗਤਾ", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "ਵਰਤੋਂ", "Valid time units:": "ਵੈਧ ਸਮਾਂ ਇਕਾਈਆਂ:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "ਵੈਰੀਏਬਲ", "variable to have them replaced with clipboard content.": "ਕਲਿੱਪਬੋਰਡ ਸਮੱਗਰੀ ਨਾਲ ਬਦਲਣ ਲਈ ਵੈਰੀਏਬਲ।", "Version": "ਵਰਜਨ", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "ਚੇਤਾਵਨੀ", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "ਚੇਤਾਵਨੀ: ਜੇ ਤੁਸੀਂ ਆਪਣਾ ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ ਅੱਪਡੇਟ ਜਾਂ ਬਦਲਦੇ ਹੋ, ਤਾਂ ਤੁਹਾਨੂੰ ਸਾਰੇ ਡਾਕੂਮੈਂਟ ਮੁੜ ਆਯਾਤ ਕਰਨ ਦੀ ਲੋੜ ਹੋਵੇਗੀ।", "Web": "ਵੈਬ", "Web API": "", "Web Loader Settings": "ਵੈਬ ਲੋਡਰ ਸੈਟਿੰਗਾਂ", "Web Search": "ਵੈੱਬ ਖੋਜ", "Web Search Engine": "ਵੈੱਬ ਖੋਜ ਇੰਜਣ", "Web Search Query Generation": "", "Webhook URL": "ਵੈਬਹੁੱਕ URL", "WebUI Settings": "ਵੈਬਯੂਆਈ ਸੈਟਿੰਗਾਂ", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "ਨਵਾਂ ਕੀ ਹੈ", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "ਕਾਰਜਸਥਲ", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "ਇੱਕ ਪ੍ਰੰਪਟ ਸੁਝਾਅ ਲਿਖੋ (ਉਦਾਹਰਣ ਲਈ ਤੁਸੀਂ ਕੌਣ ਹੋ?)", "Write a summary in 50 words that summarizes [topic or keyword].": "50 ਸ਼ਬਦਾਂ ਵਿੱਚ ਇੱਕ ਸੰਖੇਪ ਲਿਖੋ ਜੋ [ਵਿਸ਼ਾ ਜਾਂ ਕੁੰਜੀ ਸ਼ਬਦ] ਨੂੰ ਸੰਖੇਪ ਕਰਦਾ ਹੈ।", "Write something...": "", "Write your model template content here": "", "Yesterday": "ਕੱਲ੍ਹ", "You": "ਤੁਸੀਂ", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "ਤੁਹਾਡੇ ਕੋਲ ਕੋਈ ਆਰਕਾਈਵ ਕੀਤੀਆਂ ਗੱਲਾਂ ਨਹੀਂ ਹਨ।", "You have shared this chat": "ਤੁਸੀਂ ਇਹ ਗੱਲਬਾਤ ਸਾਂਝੀ ਕੀਤੀ ਹੈ", "You're a helpful assistant.": "ਤੁਸੀਂ ਇੱਕ ਮਦਦਗਾਰ ਸਹਾਇਕ ਹੋ।", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "ਯੂਟਿਊਬ", "Youtube Loader Settings": "ਯੂਟਿਊਬ ਲੋਡਰ ਸੈਟਿੰਗਾਂ"}