{"-1 for no limit, or a positive integer for a specific limit": "-1 для отсутствия ограничений или положительное целое число для определенного ограничения.", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' или '-1' чтобы был без срока годности.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(наприм<PERSON>р, `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(напри<PERSON><PERSON><PERSON>, `sh webui.sh --api`)", "(latest)": "(последняя)", "{{ models }}": "{{ модели }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Чаты {{user}}'а", "{{webUIName}} Backend Required": "Необходимо подключение к серверу {{webUIName}}", "*Prompt node ID(s) are required for image generation": "ID узлов промптов обязательны для генерации изображения", "A new version (v{{LATEST_VERSION}}) is now available.": "Новая версия (v{{LATEST_VERSION}}) теперь доступна.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Модель задач используется при выполнении таких задач, как генерация заголовков для чатов и поисковых запросов в Интернете", "a user": "пользователь", "About": "О программе", "Access": "Доступ", "Access Control": "Контроль доступа", "Accessible to all users": "Доступно всем пользователям", "Account": "Учетная запись", "Account Activation Pending": "Ожидание активации учетной записи", "Actions": "Действия", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Активируйте эту команду, набрав \"/{{COMMAND}}\" для ввода в чате.", "Active Users": "Активные пользователи", "Add": "Добавить", "Add a model ID": "Добавить ID модели", "Add a short description about what this model does": "Добавьте краткое описание того, что делает эта модель", "Add a tag": "Добавьте тег", "Add Arena Model": "Добавить модель арены", "Add Connection": "Добавить соединение", "Add Content": "Добавить контент", "Add content here": "Добавить контент сюда", "Add custom prompt": "Добавьте пользовательский промпт", "Add Files": "Добавить файлы", "Add Group": "Добавить группу", "Add Memory": "Добавить воспоминание", "Add Model": "Добавить модель", "Add Reaction": "", "Add Tag": "Добавить тег", "Add Tags": "Добавить теги", "Add text content": "", "Add User": "Добавить пользователя", "Add User Group": "Добавить группу пользователей", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Изменения в этих настройках будут применены для всех пользователей.", "admin": "админ", "Admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "Админ панель", "Admin Settings": "Настройки администратора", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Администраторы всегда имеют доступ ко всем инструментам; пользователям нужны инструменты, назначенные для каждой модели в рабочем пространстве.", "Advanced Parameters": "Расширенные Параметры", "Advanced Params": "Расширенные параметры", "All Documents": "Все документы", "All models deleted successfully": "Все модели успешно удалены", "Allow Chat Delete": "Разрешить удаление чата", "Allow Chat Deletion": "Разрешить удаление чата", "Allow Chat Edit": "Разрешить редактирование чата", "Allow File Upload": "Разрешить загрузку файлов", "Allow non-local voices": "Разрешить не локальные голоса", "Allow Temporary Chat": "Разрешить временные чаты", "Allow User Location": "Разрешить доступ к местоположению пользователя", "Allow Voice Interruption in Call": "Разрешить прерывание голоса во время вызова", "Allowed Endpoints": "", "Already have an account?": "У вас уже есть учетная запись?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "ассистент", "and": "и", "and {{COUNT}} more": "", "and create a new shared link.": "и создайте новую общую ссылку.", "API Base URL": "Базовый адрес API", "API Key": "Ключ API", "API Key created.": "Ключ API создан.", "API Key Endpoint Restrictions": "", "API keys": "Ключи API", "Application DN": "DN приложения", "Application DN Password": "DN-пароль приложения", "applies to all users with the \"user\" role": "применяется ко всем пользователям с ролью «пользователь»", "April": "Апрель", "Archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Архивировать все чаты", "Archived Chats": "<PERSON>р<PERSON><PERSON><PERSON> чатов", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "Вы уверены, что хотите разархивировать все заархивированные чаты?", "Are you sure?": "Вы уверены?", "Arena Models": "Модели арены", "Artifacts": "Артефакты", "Ask a question": "Задать вопрос", "Assistant": "Ассистент", "Attach file": "Прикрепить файл", "Attribute for Username": "Атрибут для имени пользователя", "Audio": "Аудио", "August": "Август", "Authenticate": "Аутентификация", "Auto-Copy Response to Clipboard": "Автоматическое копирование ответа в буфер обмена", "Auto-playback response": "Автоматическое воспроизведение ответа", "Autocomplete Generation": "Генерация автозаполнения", "Autocomplete Generation Input Max Length": "Максимальная длина входных данных автозаполнения", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "строка авторизации API AUTOMATIC1111", "AUTOMATIC1111 Base URL": "Базовый URL адрес AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Необходим базовый адрес URL AUTOMATIC1111.", "Available list": "Список доступных", "available!": "доступно!", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Регион Azure", "Back": "Назад", "Bad": "", "Bad Response": "Плохой ответ", "Banners": "Баннеры", "Base Model (From)": "Базовая модель (от)", "Batch Size (num_batch)": "Размер партии (num_batch)", "before": "до", "Beta": "", "Bing Search V7 Endpoint": "Конечная точка поиска Bing V7", "Bing Search V7 Subscription Key": "Ключ API Bing Search V7", "Brave Search API Key": "Ключ API поиска Brave", "By {{name}}": "", "Bypass SSL verification for Websites": "Обход проверки SSL для веб-сайтов", "Call": "Вы<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Функция вызова не поддерживается при использовании Web STT (распознавание речи) движка", "Camera": "Камера", "Cancel": "Отменить", "Capabilities": "Возможности", "Capture": "", "Certificate Path": "", "Change Password": "Изменить пароль", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "Ограничение количества символов для ввода при генерации автозаполнения", "Chart new frontiers": "Наметьте новые границы", "Chat": "Чат", "Chat Background Image": "Фоновое изображение чата", "Chat Bubble UI": "Bubble UI чат", "Chat Controls": "Управление чатом", "Chat direction": "Направление чата", "Chat Overview": "Обзор чата", "Chat Permissions": "Разрешения для чата", "Chat Tags Auto-Generation": "Автогенерация тегов чата", "Chats": "Чаты", "Check Again": "Перепроверьте ещё раз", "Check for updates": "Проверить обновления", "Checking for updates...": "Проверка обновлений...", "Choose a model before saving...": "Выберите модель перед сохранением...", "Chunk Overlap": "Перекрытие фрагментов", "Chunk Params": "Параметры фрагментов", "Chunk Size": "Размер фрагмента", "Ciphers": "Ши<PERSON><PERSON>ы", "Citation": "Цитирование", "Clear memory": "Очистить воспоминания", "click here": "кликните сюда", "Click here for filter guides.": "Нажмите здесь, чтобы просмотреть руководства по фильтрам.", "Click here for help.": "Нажмите здесь для получения помощи.", "Click here to": "Нажмите здесь, чтобы", "Click here to download user import template file.": "Нажмите здесь, чтобы загрузить файл шаблона импорта пользователя", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Нажмите здесь, чтобы выбрать", "Click here to select a csv file.": "Нажмите здесь, чтобы выбрать csv-файл.", "Click here to select a py file.": "Нажмите здесь, чтобы выбрать py-файл", "Click here to upload a workflow.json file.": "Нажмите здесь, чтобы загрузить файл workflow.json.", "click here.": "нажмите здесь.", "Click on the user role button to change a user's role.": "Нажмите кнопку роли пользователя, чтобы изменить роль пользователя.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "В разрешении на запись в буфер обмена отказано. Пожалуйста, проверьте настройки своего браузера, чтобы предоставить необходимый доступ.", "Clone": "Клонировать", "Close": "Закрыть", "Code execution": "Выполнение кода", "Code formatted successfully": "Код успешно отформатирован", "Collection": "Коллекция", "Color": "Цвет", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "Базовый адрес URL ComfyUI", "ComfyUI Base URL is required.": "Необходим базовый адрес URL ComfyUI.", "ComfyUI Workflow": "ComfyUI Workflow", "ComfyUI Workflow Nodes": "Узлы ComfyUI Workflow", "Command": "Команда", "Completions": "Завершения", "Concurrent Requests": "Одновременные запросы", "Configure": "Настроить", "Configure Models": "Настройка моделей", "Confirm": "Подтвердить", "Confirm Password": "Подтвердите пароль", "Confirm your action": "Подтвердите свое действие", "Confirm your new password": "", "Connections": "Соединение", "Contact Admin for WebUI Access": "Обратитесь к администратору для получения доступа к WebUI", "Content": "Содержание", "Content Extraction": "Извлечение контента", "Context Length": "<PERSON><PERSON><PERSON><PERSON> контекста", "Continue Response": "Продолжить ответ", "Continue with {{provider}}": "Продолжить с {{provider}}", "Continue with Email": "Продолжить с Email", "Continue with LDAP": "Продолжить с LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Управляйте разделением текста сообщения для запросов TTS. 'Пунктуация' разделяет на предложения, 'абзацы' - разделяет на абзацы, а 'нет' сохраняет сообщение в виде одной строки.", "Controls": "Управление", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "Управляет балансом между согласованностью и разнообразием выходных данных. Меньшее значение приведет к более сфокусированному и связному тексту. (По умолчанию: 5.0)", "Copied": "Скопировано", "Copied shared chat URL to clipboard!": "Копирование в буфер обмена выполнено успешно!", "Copied to clipboard": "Скопировано в буфер обмена", "Copy": "Копировать", "Copy last code block": "Копировать последний блок кода", "Copy last response": "Копировать последний ответ", "Copy Link": "Копировать ссылку", "Copy to clipboard": "Скопировать в буфер обмена", "Copying to clipboard was successful!": "Копирование в буфер обмена прошло успешно!", "Create": "Создать", "Create a knowledge base": "Создайте базу знаний", "Create a model": "Создание модели", "Create Account": "Создать аккаунт", "Create Admin Account": "Создать Аккаунт Администратора", "Create Channel": "", "Create Group": "Создать Группу", "Create Knowledge": "Создать Знания", "Create new key": "Создать новый ключ", "Create new secret key": "Создать новый секретный ключ", "Created at": "Создано", "Created At": "Создано", "Created by": "Создано", "CSV Import": "Импорт CSV", "Current Model": "Текущая модель", "Current Password": "Текущий пароль", "Custom": "Пользовательский", "Dark": "Темная", "Database": "База данных", "December": "Декабрь", "Default": "По умолчанию", "Default (Open AI)": "По умолчанию (Open AI)", "Default (SentenceTransformers)": "По умолчанию (SentenceTransformers)", "Default Model": "Модель по умолчанию", "Default model updated": "Модель по умолчанию обновлена", "Default Models": "Модели по умолчанию", "Default permissions": "Разрешения по умолчанию", "Default permissions updated successfully": "Разрешения по умолчанию успешно обновлены.", "Default Prompt Suggestions": "Предложения промптов по умолчанию", "Default to 389 or 636 if TLS is enabled": "По умолчанию 389 или 636, если <PERSON><PERSON> включен.", "Default to ALL": "По умолчанию ВСЕ", "Default User Role": "Роль пользователя по умолчанию", "Delete": "Удалить", "Delete a model": "Удалить модель", "Delete All Chats": "Удалить ВСЕ Чаты", "Delete All Models": "Удалить ВСЕ Модели", "Delete chat": "Удалить чат", "Delete Chat": "Удалить Чат", "Delete chat?": "Удалить чат?", "Delete folder?": "Удалить папку?", "Delete function?": "Удалить функцию?", "Delete Message": "", "Delete prompt?": "Удалить промпт?", "delete this link": "удалить эту ссылку", "Delete tool?": "Удалить этот инструмент?", "Delete User": "Удалить Пользователя", "Deleted {{deleteModelTag}}": "Удалено {{deleteModelTag}}", "Deleted {{name}}": "Удалено {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "Опишите свою базу знаний и цели", "Description": "Описание", "Disabled": "Отключено", "Discover a function": "Найти функцию", "Discover a model": "Найти модель", "Discover a prompt": "Найти промпт", "Discover a tool": "Найти инструмент", "Discover wonders": "Откройте для себя чудеса", "Discover, download, and explore custom functions": "Находите, загружайте и исследуйте пользовательские функции", "Discover, download, and explore custom prompts": "Находите, загружайте и исследуйте пользовательские промпты", "Discover, download, and explore custom tools": "Находите, загружайте и исследуйте пользовательские инструменты", "Discover, download, and explore model presets": "Находите, загружайте и исследуйте пользовательские предустановки моделей", "Dismissible": "Можно отклонить", "Display": "Отобража<PERSON>ь", "Display Emoji in Call": "Отображать эмодзи в вызовах", "Display the username instead of You in the Chat": "Отображать имя пользователя вместо 'Вы' в чате", "Displays citations in the response": "Отображает цитаты в ответе", "Dive into knowledge": "Погрузитесь в знания", "Do not install functions from sources you do not fully trust.": "Не устанавливайте функции из источников, которым вы не полностью доверяете.", "Do not install tools from sources you do not fully trust.": "Не устанавливайте инструменты из источников, которым вы не полностью доверяете.", "Document": "Документ", "Documentation": "Документация", "Documents": "Документы", "does not make any external connections, and your data stays securely on your locally hosted server.": "не устанавливает никаких внешних соединений, и ваши данные надежно хранятся на вашем локальном сервере.", "Don't have an account?": "У вас нет аккаунта?", "don't install random functions from sources you don't trust.": "не устанавливайте случайные функции из источников, которым вы не доверяете.", "don't install random tools from sources you don't trust.": "не устанавливайте случайные инструменты из источников, которым вы не доверяете.", "Done": "Готово", "Download": "Загрузить", "Download canceled": "Загрузка отменена", "Download Database": "Загрузить базу данных", "Drag and drop a file to upload or select a file to view": "Перетащите файл для загрузки или выберите файл для просмотра.", "Draw": "Рисовать", "Drop any files here to add to the conversation": "Перетащите сюда файлы, чтобы добавить их в разговор", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "например, '30s','10m'. Допустимые единицы времени: 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Редактировать", "Edit Arena Model": "Изменить модель арены", "Edit Channel": "", "Edit Connection": "Изменить соединение", "Edit Default Permissions": "Изменить разрешения по умолчанию", "Edit Memory": "Редактировать воспоминание", "Edit User": "Редактировать пользователя", "Edit User Group": "Редактировать Пользовательскую Группу", "ElevenLabs": "ElevenLabs", "Email": "Электронная почта", "Embark on adventures": "Отправляйтесь в приключения", "Embedding Batch Size": "Размер пакета для встраивания", "Embedding Model": "Модель встраивания", "Embedding Model Engine": "Движок модели встраивания", "Embedding model set to \"{{embedding_model}}\"": "Модель встраивания установлена в \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "Включить генерацию автозаполнения для сообщений чата", "Enable Community Sharing": "Включить совместное использование", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Включите блокировку памяти (mlock), чтобы предотвратить выгрузку данных модели из ОЗУ. Эта опция блокирует рабочий набор страниц модели в оперативной памяти, гарантируя, что они не будут выгружены на диск. Это может помочь поддерживать производительность, избегая ошибок страниц и обеспечивая быстрый доступ к данным.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Включите отображение памяти (mmap), чтобы загрузить данные модели. Эта опция позволяет системе использовать дисковое хранилище в качестве расширения оперативной памяти, обрабатывая дисковые файлы так, как если бы они находились в оперативной памяти. Это может улучшить производительность модели за счет более быстрого доступа к данным. Однако он может работать некорректно со всеми системами и занимать значительный объем дискового пространства.", "Enable Message Rating": "Разрешить оценку ответов", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Разрешить новые регистрации", "Enable Web Search": "Включить поиск в Интернете", "Enabled": "Включено", "Engine": "Движ<PERSON>к", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Убедитесь, что ваш CSV-файл включает в себя 4 столбца в следующем порядке: Имя, Электронная почта, Пароль, Роль.", "Enter {{role}} message here": "Введите сообщение {{role}} здесь", "Enter a detail about yourself for your LLMs to recall": "Введите детали о себе, чтобы LLMs могли запомнить", "Enter api auth string (e.g. username:password)": "Введите строку авторизации api (например, username:password)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Введите ключ API поиска Brave", "Enter certificate path": "Введите путь к сертификату", "Enter CFG Scale (e.g. 7.0)": "Введите CFG Scale (например, 7.0)", "Enter Chunk Overlap": "Введите перекрытие фрагмента", "Enter Chunk Size": "Введите размер фрагмента", "Enter description": "Введите описание", "Enter Github Raw URL": "Введите необработанный URL-а<PERSON><PERSON><PERSON><PERSON>", "Enter Google PSE API Key": "Введите ключ API Google PSE", "Enter Google PSE Engine Id": "Введите Id движка Google PSE", "Enter Image Size (e.g. 512x512)": "Введите размер изображения (например, 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Введите коды языков", "Enter Model ID": "Введите ID модели", "Enter model tag (e.g. {{modelTag}})": "Введите тег модели (например, {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Введите количество шагов (например, 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "Введите сэмплер (например, Euler a)", "Enter Scheduler (e.g. Karras)": "Введите планировщик (например, Karras)", "Enter Score": "Введите оценку", "Enter SearchApi API Key": "Введите ключ API SearchApi", "Enter SearchApi Engine": "Введите SearchApi движок", "Enter Searxng Query URL": "Введите URL-адрес запроса Searxng", "Enter Seed": "Введите Seed", "Enter Serper API Key": "Введите ключ API Serper", "Enter Serply API Key": "Введите ключ API Serply", "Enter Serpstack API Key": "Введите ключ API Serpstack", "Enter server host": "Введите хост сервера", "Enter server label": "Введите метку сервера", "Enter server port": "Введите порт сервера", "Enter stop sequence": "Введите последовательность остановки", "Enter system prompt": "Введите системный промпт", "Enter Tavily API Key": "Введите ключ <PERSON>ly", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Введите URL-адрес сервера Tika", "Enter Top K": "Введите Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Введите URL-адрес (например, http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Введите URL-адрес (например, http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Введите вашу электронную почту", "Enter Your Full Name": "Введите ваше полное имя", "Enter your message": "Введите ваше сообщение", "Enter your new password": "", "Enter Your Password": "Введите ваш пароль", "Enter your prompt": "", "Enter Your Role": "Введите вашу роль", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Ошибка", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Оценки", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "Исключать", "Experimental": "Экспериментальное", "Explore the cosmos": "Исследуйте космос", "Export": "Экспорт", "Export All Archived Chats": "Экспортировать ВСЕ Архивированные Чаты", "Export All Chats (All Users)": "Экспортировать все чаты (всех пользователей)", "Export chat (.json)": "Экспортировать чат (.json)", "Export Chats": "Экспортировать чаты", "Export Config to JSON File": "Экспорт конфигурации в JSON-файл", "Export Functions": "Экспортировать функции", "Export Models": "Экспортировать модели", "Export Presets": "", "Export Prompts": "Экспортировать промпты", "Export to CSV": "", "Export Tools": "Экспортировать инструменты", "External Models": "Внешние модели", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Не удалось создать ключ API.", "Failed to read clipboard contents": "Не удалось прочитать содержимое буфера обмена", "Failed to save models configuration": "", "Failed to update settings": "Не удалось обновить настройки", "February": "Февраль", "Feedback History": "История отзывов", "Feedbacks": "Отзывы", "File": "<PERSON>а<PERSON><PERSON>", "File added successfully.": "Файл успешно добавлен.", "File content updated successfully.": "Содержимое файла успешно обновлено.", "File Mode": "Режим файла", "File not found.": "Файл не найден.", "File removed successfully.": "Файл успешно удален.", "File size should not exceed {{maxSize}} MB.": "Размер файла не должен превышать {{maxSize}} МБ.", "File uploaded successfully": "", "Files": "Файлы", "Filter is now globally disabled": "Фильтр теперь отключен глобально", "Filter is now globally enabled": "Фильтр теперь включен глобально", "Filters": "Фильтры", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Определение подделки отпечатка: Невозможно использовать инициалы в качестве аватара. По умолчанию используется изображение профиля по умолчанию.", "Fluidly stream large external response chunks": "Плавная потоковая передача больших фрагментов внешних ответов", "Focus chat input": "Фокус ввода чата", "Folder deleted successfully": "Папка успешно удалена", "Folder name cannot be empty": "Имя папки не может быть пустым", "Folder name cannot be empty.": "Имя папки не может быть пустым.", "Folder name updated successfully": "Имя папки успешно обновлено", "Forge new paths": "Прокладывайте новые пути", "Form": "Форма", "Format your variables using brackets like this:": "Отформатируйте переменные, используя такие : скобки", "Frequency Penalty": "Штраф за частоту", "Function": "Функция", "Function created successfully": "Функция успешно создана", "Function deleted successfully": "Функция успешно удалена", "Function Description": "Описание Функции", "Function ID": "ID Функции", "Function is now globally disabled": "Функция теперь глобально отключена", "Function is now globally enabled": "Функция теперь глобально включена", "Function Name": "Имя Функции", "Function updated successfully": "Функция успешно обновлена", "Functions": "Функции", "Functions allow arbitrary code execution": "Функции позволяют выполнять произвольный код", "Functions allow arbitrary code execution.": "Функции позволяют выполнять произвольный код.", "Functions imported successfully": "Функции успешно импортированы", "General": "Общее", "General Settings": "Общие настройки", "Generate Image": "Сгенерировать изображение", "Generating search query": "Генерация поискового запроса", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Глобально", "Good Response": "Хороший ответ", "Google Drive": "", "Google PSE API Key": "Ключ API Google PSE", "Google PSE Engine Id": "Id движка Google PSE", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "Тактильная обратная связь", "Harmful or offensive": "", "has no conversations.": "не имеет разговоров.", "Hello, {{name}}": "Привет, {{name}}", "Help": "Помощь", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Скрыть", "Host": "", "How can I help you today?": "Чем я могу помочь вам сегодня?", "How would you rate this response?": "", "Hybrid Search": "Гибридная поисковая система", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Я подтверждаю, что прочитал и осознаю последствия своих действий. Я осознаю риски, связанные с выполнением произвольного кода, и я проверил достоверность источника.", "ID": "", "Ignite curiosity": "Разожгите любопытство", "Image Compression": "", "Image Generation (Experimental)": "Генерация изображений (Экспериментально)", "Image Generation Engine": "Механизм генерации изображений", "Image Max Compression Size": "", "Image Settings": "Настройки изображения", "Images": "Изображения", "Import Chats": "Импортировать Чаты", "Import Config from JSON File": "Импорт конфигурации из JSON-файла", "Import Functions": "Импортировать Функции", "Import Models": "Импортировать Модели", "Import Presets": "Импортировать Пресеты", "Import Prompts": "Импортировать Промпты", "Import Tools": "Импортировать Инструменты", "Include": "Включать", "Include `--api-auth` flag when running stable-diffusion-webui": "Добавьте флаг '--api-auth' при запуске stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Добавьте флаг `--api` при запуске stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Информация", "Input commands": "Введите команды", "Install from Github URL": "Установка с URL-адрес<PERSON> Github", "Instant Auto-Send After Voice Transcription": "Мгновенная автоматическая отправка после расшифровки голоса", "Interface": "Интерфейс", "Invalid file format.": "Неверный формат файла.", "Invalid Tag": "Недопустимый тег", "is typing...": "", "January": "Январь", "Jina API Key": "", "join our Discord for help.": "присоединяйтесь к нашему Discord для помощи.", "JSON": "JSON", "JSON Preview": "Предварительный просмотр JSON", "July": "Июль", "June": "Июнь", "JWT Expiration": "Истечение срока JWT", "JWT Token": "Токен JWT", "Kagi Search API Key": "", "Keep Alive": "Поддерживать активность", "Key": "", "Keyboard shortcuts": "Горячие клавиши", "Knowledge": "Знания", "Knowledge Access": "Доступ к Знаниям", "Knowledge created successfully.": "Знания созданы успешно.", "Knowledge deleted successfully.": "Знания успешно удалены.", "Knowledge reset successfully.": "Знания успешно сброшены.", "Knowledge updated successfully": "Знания успешно обновлены", "Label": "", "Landing Page Mode": "Режим Целевой Страницы", "Language": "Язык", "Last Active": "Последний Активный", "Last Modified": "Последнее Изменение", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "LDAP сервер обновлен", "Leaderboard": "Таблиц<PERSON>", "Leave empty for unlimited": "Оставьте пустым для неограниченного", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "Оставьте пустым, чтобы использовать промпт по умолчанию, или введите пользовательский промпт", "Light": "Светлый", "Listening...": "Слушаю...", "Local": "", "Local Models": "Локальные модели", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Сделано сообществом OpenWebUI", "Make sure to enclose them with": "Убедитесь, что они заключены в", "Make sure to export a workflow.json file as API format from ComfyUI.": "Убедитесь, что экспортируете файл workflow.json в формате API из ComfyUI.", "Manage": "Управлять", "Manage Arena Models": "Управление Ареной Моделей", "Manage Ollama": "Управление Ollama", "Manage Ollama API Connections": "Управление соединения<PERSON><PERSON> <PERSON>", "Manage OpenAI API Connections": "Управление соединениями API OpenAI", "Manage Pipelines": "Управление конвейерами", "March": "Ма<PERSON><PERSON>", "Max Tokens (num_predict)": "Максимальное количество токенов (num_predict)", "Max Upload Count": "Максимальное количество загрузок", "Max Upload Size": "Максимальный размер загрузок", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Максимальное количество моделей для загрузки одновременно - 3. Пожалуйста, попробуйте позже.", "May": "<PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Воспоминания, доступные LLMs, будут отображаться здесь.", "Memory": "Воспоминания", "Memory added successfully": "Воспоминание успешно добавлено", "Memory cleared successfully": "Воспоминания успешно очищены", "Memory deleted successfully": "Воспоминание успешно удалено", "Memory updated successfully": "Воспоминание успешно обновлено", "Merge Responses": "Объединить ответы", "Message rating should be enabled to use this feature": "Чтобы использовать эту функцию, необходимо включить оценку сообщения.", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Сообщения, отправленные вами после создания ссылки, не будут передаваться другим. Пользователи, у которых есть URL, смогут просматривать общий чат.", "Min P": "<PERSON>", "Minimum Score": "Минимальный балл", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "DD MMMM YYYY", "MMMM DD, YYYY HH:mm": "DD MMMM YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY hh:mm:ss A", "Model": "Модель", "Model '{{modelName}}' has been successfully downloaded.": "Модель '{{modelName}}' успешно загружена.", "Model '{{modelTag}}' is already in queue for downloading.": "Модель '{{modelTag}}' уже находится в очереди на загрузку.", "Model {{modelId}} not found": "Модель {{modelId}} не найдена", "Model {{modelName}} is not vision capable": "Модель {{modelName}} не поддерживает зрение", "Model {{name}} is now {{status}}": "Модель {{name}} теперь {{status}}", "Model accepts image inputs": "Модель принимает изображения как входные данные", "Model created successfully!": "Модель успешно создана!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Обнаружен путь к файловой системе модели. Для обновления требуется краткое имя модели, не удается продолжить.", "Model Filtering": "Фильтрация Моделей", "Model ID": "ID Модели", "Model IDs": "IDs Модели", "Model Name": "Имя Модели", "Model not selected": "Модель не выбрана", "Model Params": "Параметры модели", "Model Permissions": "Разрешения Модели", "Model updated successfully": "Модель успешно обновлена", "Modelfile Content": "Содержимое файла модели", "Models": "Модели", "Models Access": "Доступ к Моделям", "Models configuration saved successfully": "Конфигурация модели успешно сохранена.", "Mojeek Search API Key": "", "more": "", "More": "Больше", "Name": "Имя", "Name your knowledge base": "", "New Chat": "Новый чат", "New folder": "", "New Password": "Новый пароль", "new-channel": "", "No content found": "Контент не найден", "No content to speak": "Нечего говорить", "No distance available": "", "No feedbacks found": "", "No file selected": "Файлы не выбраны", "No files found.": "Файлы не найдены.", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "Знания не найдены", "No model IDs": "", "No models found": "Модели не найдены", "No models selected": "Модели не выбраны", "No results found": "Результатов не найдено", "No search query generated": "Поисковый запрос не сгенерирован", "No source available": "Нет доступных источников", "No users were found.": "Пользователи не найдены.", "No valves to update": "Нет вентилей для обновления", "None": "Нет", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Обратите внимание: Если вы установите минимальный балл, поиск будет возвращать только документы с баллом больше или равным минимальному баллу.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Уведомления", "November": "Ноябрь", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "Октябрь", "Off": "Выключено", "Okay, Let's Go!": "Давайте начнём!", "OLED Dark": "OLED темная", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API отключен", "Ollama API settings updated": "", "Ollama Version": "Версия Ollama", "On": "Включено", "Only alphanumeric characters and hyphens are allowed": "Разрешены только буквенно-цифровые символы и дефисы.", "Only alphanumeric characters and hyphens are allowed in the command string.": "В строке команды разрешено использовать только буквенно-цифровые символы и дефисы.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "Доступ имеют только избранные пользователи и группы, имеющие разрешение.", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Упс! Похоже, что URL-адрес недействителен. Пожалуйста, перепроверьте и попробуйте еще раз.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Упс! Есть файлы, которые все еще загружаются. Пожалуйста, дождитесь завершения загрузки.", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Упс! Вы используете неподдерживаемый метод (только фронтенд). Пожалуйста, обслуживайте веб-интерфейс из бэкенда.", "Open in full screen": "Открыть на весь экран", "Open new chat": "Открыть новый чат", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Версия Open WebUI (v{{OPEN_WEBUI_VERSION}}) ниже требуемой версии (v{{REQUIRED_VERSION}})", "OpenAI": "Open AI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Конфигурация API OpenAI", "OpenAI API Key is required.": "Требуется ключ API OpenAI.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "Требуется URL-адрес API OpenAI или ключ API.", "or": "или", "Organize your users": "Организуйте своих пользователей", "OUTPUT": "", "Output format": "Формат вывода", "Overview": "Обзор", "page": "страница", "Password": "Пароль", "Paste Large Text as File": "Вставить большой текст как файл", "PDF document (.pdf)": "PDF-документ (.pdf)", "PDF Extract Images (OCR)": "Извлечение изображений из PDF (OCR)", "pending": "ожидающий", "Permission denied when accessing media devices": "Отказано в разрешении на доступ к мультимедийным устройствам", "Permission denied when accessing microphone": "Отказано в разрешении на доступ к микрофону", "Permission denied when accessing microphone: {{error}}": "Отказано в разрешении на доступ к микрофону: {{error}}", "Permissions": "Разрешения", "Personalization": "Персонализация", "Pin": "Закрепить", "Pinned": "Закреплено", "Pioneer insights": "Новаторские идеи", "Pipeline deleted successfully": "Конвейер успешно удален", "Pipeline downloaded successfully": "Конвейер успешно загружен", "Pipelines": "Конвейеры", "Pipelines Not Detected": "Конвейеры не обнаружены", "Pipelines Valves": "Вентили конвейеров", "Plain text (.txt)": "Текст в формате .txt", "Playground": "Песочница", "Please carefully review the following warnings:": "Пожалуйста, внимательно ознакомьтесь со следующими предупреждениями:", "Please enter a prompt": "Пожалуйста, введите подсказку", "Please fill in all fields.": "Пожалуйста, заполните все поля.", "Please select a model first.": "Пожалуйста, сначала выберите модель.", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Предыдущие 30 дней", "Previous 7 days": "Предыдущие 7 дней", "Profile Image": "Изображение профиля", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Промпт (напри<PERSON><PERSON><PERSON>, Расскажи мне интересный факт о Римской империи)", "Prompt Content": "Содержание промпта", "Prompt created successfully": "", "Prompt suggestions": "Предложения промптов", "Prompt updated successfully": "", "Prompts": "Промпты", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Загрузить \"{{searchValue}}\" с Ollama.com", "Pull a model from Ollama.com": "Загрузить модель с Ollama.com", "Query Generation Prompt": "", "Query Params": "Параметры запроса", "RAG Template": "Шаблон RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "Прочитать вслух", "Record voice": "Записать голос", "Redirecting you to OpenWebUI Community": "Перенаправляем вас в сообщество OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Называйте себя \"User\" (например, \"User is learning Spanish\").", "References from": "", "Refresh Token Expiration": "", "Regenerate": "Перегенерировать", "Release Notes": "Примечания к выпуску", "Relevance": "", "Remove": "Удалить", "Remove Model": "Удалить модель", "Rename": "Переименовать", "Reorder Models": "", "Repeat Last N": "Повторить последние N", "Reply in Thread": "", "Request Mode": "Режим запроса", "Reranking Model": "Модель реранжирования", "Reranking model disabled": "Модель реранжирования отключена", "Reranking model set to \"{{reranking_model}}\"": "Модель реранжирования установлена на \"{{reranking_model}}\"", "Reset": "Сбросить", "Reset All Models": "", "Reset Upload Directory": "Сбросить каталог загрузок", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Уведомления об ответах не могут быть активированы, поскольку доступ к веб-сайту был заблокирован. Пожалуйста, перейдите к настройкам своего браузера, чтобы предоставить необходимый доступ.", "Response splitting": "Разделение ответов", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Ввод форматированного текста для чата", "RK": "", "Role": "Роль", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Запустить", "Running": "Выполняется", "Save": "Сохранить", "Save & Create": "Сохранить и создать", "Save & Update": "Сохранить и обновить", "Save As Copy": "Сохранить как копию", "Save Tag": "Сохранить тег", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Прямое сохранение журналов чата в хранилище вашего браузера больше не поддерживается. Пожалуйста, потратьте минуту, чтобы скачать и удалить ваши журналы чата, нажав на кнопку ниже. Не волнуйтесь, вы легко сможете повторно импортировать свои журналы чата в бэкенд через", "Scroll to bottom when switching between branches": "Прокручивать вниз при переключении веток", "Search": "Поиск", "Search a model": "Поиск модели", "Search Base": "", "Search Chats": "Поиск в чатах", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "Поиск функций", "Search Knowledge": "", "Search Models": "Поиск моделей", "Search options": "", "Search Prompts": "Поиск промптов", "Search Result Count": "Количество результатов поиска", "Search Tools": "Поиск инструментов", "Search users": "", "SearchApi API Key": "Ключ SearchApi API", "SearchApi Engine": "Движок SearchApi", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "Поиск \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Поиск знания для \"{{searchQuery}}\"", "Searxng Query URL": "URL-адрес запроса Searxng", "See readme.md for instructions": "Смотрите readme.md для инструкций", "See what's new": "Посмотреть, что нового", "Seed": "Сид", "Select a base model": "Выберите базовую модель", "Select a engine": "Выберите движок", "Select a function": "Выберите функцию", "Select a group": "", "Select a model": "Выберите модель", "Select a pipeline": "Выберите конвейер", "Select a pipeline url": "Выберите URL-адрес конвейера", "Select a tool": "Выберите инструмент", "Select Engine": "Выберите движок", "Select Knowledge": "", "Select model": "Выберите модель", "Select only one model to call": "Выберите только одну модель для вызова", "Selected model(s) do not support image inputs": "Выбранные модели не поддерживают ввод изображений", "Semantic distance to query": "", "Send": "Отправить", "Send a message": "", "Send a Message": "Отправить сообщение", "Send message": "Отправить сообщение", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Отправляет в запросе \"stream_options: { include_usage: true }\".\nПоддерживаемые провайдеры будут возвращать информацию об использовании токена в ответе, когда это будет установлено.", "September": "Сентябрь", "Serper API Key": "Ключ API Serper", "Serply API Key": "Ключ API Serply", "Serpstack API Key": "Ключ API Serpstack", "Server connection verified": "Соединение с сервером проверено", "Set as default": "Установить по умолчанию", "Set CFG Scale": "Установить CFG Scale", "Set Default Model": "Установить модель по умолчанию", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Установить модель эмбеддинга (например, {{model}})", "Set Image Size": "Установить размер изображения", "Set reranking model (e.g. {{model}})": "Установить модель реранжирования (например, {{model}})", "Set Sampler": "Установить сэмплер", "Set Scheduler": "Установить планировщик", "Set Steps": "Установить шаги", "Set Task Model": "Установить модель задачи", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Установить голос", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Настройки", "Settings saved successfully!": "Настройки успешно сохранены!", "Share": "Поделиться", "Share Chat": "Поделиться чатом", "Share to OpenWebUI Community": "Поделиться с сообществом OpenWebUI", "Show": "Показать", "Show \"What's New\" modal on login": "Показывать окно «Что нового» при входе в систему", "Show Admin Details in Account Pending Overlay": "Показывать данные администратора в оверлее ожидающей учетной записи", "Show shortcuts": "Показать горячие клавиши", "Show your support!": "Поддержите нас!", "Sign in": "Войти", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Выйти", "Sign up": "Зарегистрироваться", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Источник", "Speech Playback Speed": "Скорость воспроизведения речи", "Speech recognition error: {{error}}": "Ошибка распознавания речи: {{error}}", "Speech-to-Text Engine": "Система распознавания речи", "Stop": "", "Stop Sequence": "Последовательность остановки", "Stream Chat Response": "Потоковый вывод ответа", "STT Model": "Модель распознавания речи", "STT Settings": "Настройки распознавания речи", "Success": "Успех", "Successfully updated.": "Успешно обновлено.", "Suggested prompts to get you started": "", "Support": "Поддержать", "Support this plugin:": "Поддержите этот плагин", "Sync directory": "", "System": "Система", "System Instructions": "", "System Prompt": "Системный промпт", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Нажмите, чтобы прервать", "Tavily API Key": "Ключ <PERSON> Tavily", "Temperature": "Температура", "Template": "Шабл<PERSON>н", "Temporary Chat": "Временный чат", "Text Splitter": "", "Text-to-Speech Engine": "Система синтеза речи", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Спасибо за вашу обратную связь!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Разработчики этого плагина - увлеченные волонтеры из сообщества. Если вы считаете этот плагин полезным, пожалуйста, подумайте о том, чтобы внести свой вклад в его разработку.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Максимальный размер файла в МБ. Если размер файла превысит это ограничение, файл не будет загружен.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Максимальное количество файлов, которые могут быть использованы одновременно в чате. Если количество файлов превысит это ограничение, файлы не будут загружены.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Оценка должна быть значением между 0,0 (0%) и 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "Температура модели. Повышение температуры заставит модель отвечать более творчески. (По умолчанию: 0,8)", "Theme": "Тема", "Thinking...": "Думаю...", "This action cannot be undone. Do you wish to continue?": "Это действие нельзя отменить. Вы хотите продолжить?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Это обеспечивает сохранение ваших ценных разговоров в безопасной базе данных на вашем сервере. Спасибо!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Это экспериментальная функция, она может работать не так, как ожидалось, и может быть изменена в любое время.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "Этот параметр определяет, сколько токенов сохраняется при обновлении контекста. Например, если установлено значение 2, будут сохранены два последних токена контекста разговора. Сохранение контекста может помочь сохранить непрерывность разговора, но может снизить способность реагировать на новые темы. (По умолчанию: 24)", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "Этот параметр устанавливает максимальное количество токенов, которые модель может сгенерировать в своем ответе. Увеличение этого предела позволяет модели предоставлять более длинные ответы, но также может увеличить вероятность создания бесполезного или нерелевантного контента. (По умолчанию: 128)", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Эта опция удалит все существующие файлы в коллекции и заменит их вновь загруженными файлами.", "This response was generated by \"{{model}}\"": "", "This will delete": "Это приведет к удалению", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "При этом будет удален <strong>{{NAME}}</strong> и <strong>все его содержимое</strong>.", "This will delete all models including custom models": "Это приведет к удалению всех моделей, включая пользовательские модели.", "This will delete all models including custom models and cannot be undone.": "При этом будут удалены все модели, включая пользовательские, и это действие нельзя будет отменить.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Это сбросит базу знаний и синхронизирует все файлы. Хотите продолжить?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Требуется URL-адрес сервера Tika.", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Совет: Обновляйте несколько переменных подряд, нажимая клавишу Tab в поле ввода чата после каждой замены.", "Title": "Заголовок", "Title (e.g. Tell me a fun fact)": "Заголовок (напри<PERSON><PERSON><PERSON>, Расскажи мне интересный факт)", "Title Auto-Generation": "Автогенерация заголовка", "Title cannot be an empty string.": "Заголовок не может быть пустой строкой.", "Title Generation Prompt": "Промпт для генерации заголовка", "TLS": "", "To access the available model names for downloading,": "Чтобы получить доступ к доступным для загрузки именам моделей,", "To access the GGUF models available for downloading,": "Чтобы получить доступ к моделям GGUF, доступным для загрузки,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Чтобы получить доступ к WebUI, пожалуйста, обратитесь к администратору. Администраторы могут управлять статусами пользователей из панели администратора.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Чтобы прикрепить сюда базу знаний, сначала добавьте её в \"Знания\" рабочего пространства.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "Чтобы выбрать действия, сначала добавьте их в \"Функции\" рабочего пространства.", "To select filters here, add them to the \"Functions\" workspace first.": "Чтобы выбрать фильтры, сначала добавьте их в \"Функции\" рабочего пространства.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Чтобы выбрать инструменты, сначала добавьте их в \"Инструменты\" рабочего пространства.", "Toast notifications for new updates": "Уведомления о обновлениях", "Today": "Сегодня", "Toggle settings": "Переключить настройки", "Toggle sidebar": "Переключить боковую панель", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "Количество токенов для сохранения при обновлении контекста (num_keep)", "Tool created successfully": "Инструмент успешно создан", "Tool deleted successfully": "Инструмент успешно удален", "Tool Description": "Описание Инструмента", "Tool ID": "ID Инструмента", "Tool imported successfully": "Инструмент успешно импортирован", "Tool Name": "Имя Инструмента", "Tool updated successfully": "Инструмент успешно обновлен", "Tools": "Инструменты", "Tools Access": "Доступ к инструментам", "Tools are a function calling system with arbitrary code execution": "Инструменты - это система вызова функций с выполнением произвольного кода", "Tools have a function calling system that allows arbitrary code execution": "Инструменты имеют систему вызова функций, которая позволяет выполнять произвольный код", "Tools have a function calling system that allows arbitrary code execution.": "Инструменты имеют систему вызова функций, которая позволяет выполнять произвольный код.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "Проблемы с доступом к Ollama?", "TTS Model": "Модель TTS", "TTS Settings": "Настройки TTS", "TTS Voice": "Голос TTS", "Type": "Тип", "Type Hugging Face Resolve (Download) URL": "Введите URL-адрес Hugging Face Resolve (загрузки)", "Uh-oh! There was an issue with the response.": "", "UI": "Пользовательский интерфейс", "Unarchive All": "Разархивировать ВСЁ", "Unarchive All Archived Chats": "Разархивировать ВСЕ Заархивированные Чаты", "Unarchive Chat": "Разархивировать чат", "Unlock mysteries": "Разблокируйте тайны", "Unpin": "Открепить", "Unravel secrets": "Разгадать секреты", "Untagged": "Без тегов", "Update": "Обновить", "Update and Copy Link": "Обновить и скопировать ссылку", "Update for the latest features and improvements.": "Обновитесь для получения последних функций и улучшений.", "Update password": "Обновить пароль", "Updated": "Обновлено", "Updated at": "Обновлено", "Updated At": "", "Upload": "Загрузить", "Upload a GGUF model": "Загрузить модель GGUF", "Upload directory": "Загрузить каталог", "Upload files": "Загрузить файлы", "Upload Files": "Загрузить файлы", "Upload Pipeline": "Загрузить конвейер", "Upload Progress": "Прогресс загрузки", "URL": "", "URL Mode": "Режим URL", "Use '#' in the prompt input to load and include your knowledge.": "Используйте «#» в строке ввода, чтобы загрузить и включить свои знания.", "Use Gravatar": "Использовать Gravatar", "Use groups to group your users and assign permissions.": "Используйте группы, чтобы группировать пользователей и назначать разрешения.", "Use Initials": "Использовать инициалы", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "пользователь", "User": "Пользователь", "User location successfully retrieved.": "Местоположение пользователя успешно получено.", "Username": "Имя пользователя", "Users": "Пользователи", "Using the default arena model with all models. Click the plus button to add custom models.": "Использование модели арены по умолчанию со всеми моделями. Нажмите кнопку «плюс», чтобы добавить пользовательские модели.", "Utilize": "Используйте", "Valid time units:": "Допустимые единицы времени:", "Valves": "Вентили", "Valves updated": "Вентили обновлены", "Valves updated successfully": "Вентили успешно обновлены", "variable": "переменная", "variable to have them replaced with clipboard content.": "перемен<PERSON><PERSON>ю, чтобы заменить их содержимым буфера обмена.", "Version": "Версия", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "Видимость", "Voice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Voice Input": "", "Warning": "Предупреждение", "Warning:": "Предупреждение:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Предупреждение. Включение этого параметра позволит пользователям загружать произвольный код на сервер.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Предупреждение: Если вы обновите или измените модель эмбеддинга, вам нужно будет повторно импортировать все документы.", "Web": "<PERSON>е<PERSON>", "Web API": "Веб API", "Web Loader Settings": "Настройки веб-загрузчика", "Web Search": "Веб-поиск", "Web Search Engine": "Поисковая система", "Web Search Query Generation": "Генерация запросов веб-поиска", "Webhook URL": "URL-адрес веб-хука", "WebUI Settings": "Настройки WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI будет отправлять запросы к \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI будет отправлять запросы к \"{{url}}/chat/completions\"", "Welcome, {{name}}!": "", "What are you trying to achieve?": "Чего вы пытаетесь достичь?", "What are you working on?": "Над чем вы работаете?", "What didn't you like about this response?": "", "What’s New in": "Что нового в", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Если эта функция включена, модель будет отвечать на каждое сообщение чата в режиме реального времени, генерируя ответ, как только пользователь отправит сообщение. Этот режим полезен для приложений живого чата, но может повлиять на производительность на более медленном оборудовании.", "wherever you are": "где бы ты ни был", "Whisper (Local)": "<PERSON>hisper (Локально)", "Widescreen Mode": "Широкоэкранный режим", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "Работает совместно с top-k. Более высокое значение (например, 0,95) приведет к созданию более разнообразного текста, а более низкое значение (например, 0,5) приведет к созданию более сфокусированного и консервативного текста. (По умолчанию: 0,9)", "Workspace": "Рабочее пространство", "Workspace Permissions": "Разрешения для Рабочего пространства", "Write a prompt suggestion (e.g. Who are you?)": "Напишите предложение промпта (например, Кто вы?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Напишите резюме в 50 словах, которое кратко описывает [тему или ключевое слово].", "Write something...": "Напишите что-нибудь...", "Write your model template content here": "Напишите здесь содержимое шаблона вашей модели.", "Yesterday": "Вчера", "You": "Вы", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Одновременно вы можете общаться только с максимальным количеством файлов {{maxCount}}.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Вы можете персонализировать свое взаимодействие с LLMs, добавив воспоминания с помощью кнопки \"Управлять\" ниже, что сделает их более полезными и адаптированными для вас.", "You cannot upload an empty file.": "Вы не можете загрузить пустой файл.", "You have no archived conversations.": "У вас нет архивированных бесед.", "You have shared this chat": "Вы поделились этим чатом", "You're a helpful assistant.": "Вы полезный ассистент.", "Your account status is currently pending activation.": "В настоящее время ваша учетная запись ожидает активации.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Весь ваш взнос будет направлен непосредственно разработчику плагина; Open WebUI не взимает никаких процентов. Однако выбранная платформа финансирования может иметь свои собственные сборы.", "Youtube": "YouTube", "Youtube Loader Settings": "Настройки загрузчика YouTube"}