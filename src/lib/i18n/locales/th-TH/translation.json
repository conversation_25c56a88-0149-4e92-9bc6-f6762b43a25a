{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' หรือ '-1' สำหรับไม่มีการหมดอายุ", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(เช่น `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(เช่น `sh webui.sh --api`)", "(latest)": "(ล่าสุด)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "การสนทนาของ {{user}}", "{{webUIName}} Backend Required": "ต้องการ Backend ของ {{webUIName}}", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "ใช้โมเดลงานเมื่อทำงานเช่นการสร้างหัวข้อสำหรับการสนทนาและการค้นหาเว็บ", "a user": "ผู้ใช้", "About": "เกี่ยวกับ", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "บัญชี", "Account Activation Pending": "การเปิดใช้งานบัญชีอยู่ระหว่างดำเนินการ", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "ผู้ใช้ที่ใช้งานอยู่", "Add": "เพิ่ม", "Add a model ID": "", "Add a short description about what this model does": "เพิ่มคำอธิบายสั้นๆ เกี่ยวกับสิ่งที่โมเดลนี้ทำ", "Add a tag": "เพิ่มแท็ก", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "เพิ่มพรอมต์ที่กำหนดเอง", "Add Files": "เพิ่มไฟล์", "Add Group": "", "Add Memory": "เพิ่มความจำ", "Add Model": "เพิ่มโมเดล", "Add Reaction": "", "Add Tag": "", "Add Tags": "เพิ่มแท็ก", "Add text content": "", "Add User": "เพิ่มผู้ใช้", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "การปรับการตั้งค่าเหล่านี้จะนำไปใช้กับผู้ใช้ทั้งหมด", "admin": "ผู้ดูแลระบบ", "Admin": "ผู้ดูแลระบบ", "Admin Panel": "แผงผู้ดูแลระบบ", "Admin Settings": "การตั้งค่าผู้ดูแลระบบ", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "ผู้ดูแลระบบสามารถเข้าถึงเครื่องมือทั้งหมดได้ตลอดเวลา; ผู้ใช้ต้องการเครื่องมือที่กำหนดต่อโมเดลในพื้นที่ทำงาน", "Advanced Parameters": "พารามิเตอร์ขั้นสูง", "Advanced Params": "พารามิเตอร์ขั้นสูง", "All Documents": "เอกสารทั้งหมด", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "อนุญาตการลบการสนทนา", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "อนุญาตเสียงที่ไม่ใช่ท้องถิ่น", "Allow Temporary Chat": "", "Allow User Location": "อนุญาตตำแหน่งผู้ใช้", "Allow Voice Interruption in Call": "อนุญาตการแทรกเสียงในสาย", "Allowed Endpoints": "", "Already have an account?": "มีบัญชีอยู่แล้ว?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "ผู้ช่วย", "and": "และ", "and {{COUNT}} more": "", "and create a new shared link.": "และสร้างลิงก์ที่แชร์ใหม่", "API Base URL": "URL ฐานของ API", "API Key": "คีย์ API", "API Key created.": "สร้างคีย์ API แล้ว", "API Key Endpoint Restrictions": "", "API keys": "คีย์ API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "เมษายน", "Archive": "เก็บถาวร", "Archive All Chats": "เก็บถาวรการสนทนาทั้งหมด", "Archived Chats": "การสนทนาที่เก็บถาวร", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "คุณแน่ใจหรือ?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "แนบไฟล์", "Attribute for Username": "", "Audio": "เสียง", "August": "สิงหาคม", "Authenticate": "", "Auto-Copy Response to Clipboard": "ตอบสนองการคัดลอกอัตโนมัติไปยังคลิปบอร์ด", "Auto-playback response": "ตอบสนองการเล่นอัตโนมัติ", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "สตริงการตรวจสอบ API ของ AUTOMATIC1111", "AUTOMATIC1111 Base URL": "URL ฐานของ AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "ต้องการ URL ฐานของ AUTOMATIC1111", "Available list": "", "available!": "พร้อมใช้งาน!", "Azure AI Speech": "", "Azure Region": "", "Back": "กลับ", "Bad": "", "Bad Response": "การตอบสนองที่ไม่ดี", "Banners": "แบนเนอร์", "Base Model (From)": "โมเดลพื้นฐาน (จาก)", "Batch Size (num_batch)": "ขนาดชุด (num_batch)", "before": "ก่อน", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "คีย์ API ของ Brave Search", "By {{name}}": "", "Bypass SSL verification for Websites": "ข้ามการตรวจสอบ SSL สำหรับเว็บไซต์", "Call": "โทร", "Call feature is not supported when using Web STT engine": "ไม่รองรับฟีเจอร์การโทรเมื่อใช้เครื่องยนต์ Web STT", "Camera": "กล้อง", "Cancel": "ยกเลิก", "Capabilities": "ความสามารถ", "Capture": "", "Certificate Path": "", "Change Password": "เปลี่ยนรหัสผ่าน", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "แชท", "Chat Background Image": "ภาพพื้นหลังแชท", "Chat Bubble UI": "UI ฟองแชท", "Chat Controls": "การควบคุมแชท", "Chat direction": "ทิศทางการแชท", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "แชท", "Check Again": "ตรวจสอบอีกครั้ง", "Check for updates": "ตรวจสอบการอัปเดต", "Checking for updates...": "กำลังตรวจสอบการอัปเดต...", "Choose a model before saving...": "เลือกโมเดลก่อนบันทึก...", "Chunk Overlap": "ทับซ้อนส่วนข้อมูล", "Chunk Params": "พารามิเตอร์ส่วนข้อมูล", "Chunk Size": "ขนาดส่วนข้อมูล", "Ciphers": "", "Citation": "การอ้างอิง", "Clear memory": "ล้างความจำ", "click here": "", "Click here for filter guides.": "", "Click here for help.": "คลิกที่นี่เพื่อขอความช่วยเหลือ", "Click here to": "คลิกที่นี่เพื่อ", "Click here to download user import template file.": "คลิกที่นี่เพื่อดาวน์โหลดไฟล์แม่แบบนำเข้าผู้ใช้", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "คลิกที่นี่เพื่อเลือก", "Click here to select a csv file.": "คลิกที่นี่เพื่อเลือกไฟล์ csv", "Click here to select a py file.": "คลิกที่นี่เพื่อเลือกไฟล์ py", "Click here to upload a workflow.json file.": "", "click here.": "คลิกที่นี่", "Click on the user role button to change a user's role.": "คลิกที่ปุ่มบทบาทผู้ใช้เพื่อเปลี่ยนบทบาทของผู้ใช้", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "การอนุญาตเขียนคลิปบอร์ดถูกปฏิเสธ โปรดตรวจสอบการตั้งค่าเบราว์เซอร์ของคุณเพื่อให้สิทธิ์ที่จำเป็น", "Clone": "โคลน", "Close": "ปิด", "Code execution": "", "Code formatted successfully": "จัดรูปแบบโค้ดสำเร็จแล้ว", "Collection": "คอลเลคชัน", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL ฐานของ ComfyUI", "ComfyUI Base URL is required.": "ต้องการ URL ฐานของ ComfyUI", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "คำสั่ง", "Completions": "", "Concurrent Requests": "คำขอพร้อมกัน", "Configure": "", "Configure Models": "", "Confirm": "ยืนยัน", "Confirm Password": "ยืนยันรหัสผ่าน", "Confirm your action": "ยืนยันการดำเนินการของคุณ", "Confirm your new password": "", "Connections": "การเชื่อมต่อ", "Contact Admin for WebUI Access": "ติดต่อผู้ดูแลระบบสำหรับการเข้าถึง WebUI", "Content": "เนื้อหา", "Content Extraction": "การสกัดเนื้อหา", "Context Length": "ความยาวของบริบท", "Continue Response": "ตอบสนองต่อไป", "Continue with {{provider}}": "ดำเนินการต่อด้วย {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "การควบคุม", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "คัดลอก URL แชทที่แชร์ไปยังคลิปบอร์ดแล้ว!", "Copied to clipboard": "", "Copy": "คัดลอก", "Copy last code block": "คัดลอกบล็อกโค้ดสุดท้าย", "Copy last response": "คัดลอกการตอบสนองล่าสุด", "Copy Link": "คัดลอกลิงก์", "Copy to clipboard": "", "Copying to clipboard was successful!": "คัดลอกไปยังคลิปบอร์ดสำเร็จแล้ว!", "Create": "", "Create a knowledge base": "", "Create a model": "สร้างโมเดล", "Create Account": "สร้างบัญชี", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "สร้างคีย์ใหม่", "Create new secret key": "สร้างคีย์ลับใหม่", "Created at": "สร้างเมื่อ", "Created At": "สร้างเมื่อ", "Created by": "สร้างโดย", "CSV Import": "นำเข้า CSV", "Current Model": "โมเดลปัจจุบัน", "Current Password": "รหัสผ่านปัจจุบัน", "Custom": "กำหนดเอง", "Dark": "มืด", "Database": "ฐานข้อมูล", "December": "ธันวาคม", "Default": "ค่าเริ่มต้น", "Default (Open AI)": "", "Default (SentenceTransformers)": "ค่าเริ่มต้น (SentenceTransformers)", "Default Model": "โมเดลค่าเริ่มต้น", "Default model updated": "อัปเดตโมเดลค่าเริ่มต้นแล้ว", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "คำแนะนำพรอมต์ค่าเริ่มต้น", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "บทบาทผู้ใช้ค่าเริ่มต้น", "Delete": "ลบ", "Delete a model": "ลบโมเดล", "Delete All Chats": "ลบการสนทนาทั้งหมด", "Delete All Models": "", "Delete chat": "ลบแชท", "Delete Chat": "ลบแชท", "Delete chat?": "ลบแชท?", "Delete folder?": "", "Delete function?": "ลบฟังก์ชัน?", "Delete Message": "", "Delete prompt?": "ลบพรอมต์?", "delete this link": "ลบลิงก์นี้", "Delete tool?": "ลบเครื่องมือ?", "Delete User": "ลบผู้ใช้", "Deleted {{deleteModelTag}}": "ลบ {{deleteModelTag}}", "Deleted {{name}}": "ลบ {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "คำอธิบาย", "Disabled": "ปิดใช้งาน", "Discover a function": "ค้นหาฟังก์ชัน", "Discover a model": "ค้นหาโมเดล", "Discover a prompt": "ค้นหาพรอมต์", "Discover a tool": "ค้นหาเครื่องมือ", "Discover wonders": "", "Discover, download, and explore custom functions": "ค้นหา ดาวน์โหลด และสำรวจฟังก์ชันที่กำหนดเอง", "Discover, download, and explore custom prompts": "ค้นหา ดาวน์โหลด และสำรวจพรอมต์ที่กำหนดเอง", "Discover, download, and explore custom tools": "ค้นหา ดาวน์โหลด และสำรวจเครื่องมือที่กำหนดเอง", "Discover, download, and explore model presets": "ค้นหา ดาวน์โหลด และสำรวจพรีเซ็ตโมเดล", "Dismissible": "ยกเลิกได้", "Display": "", "Display Emoji in Call": "แสดงอิโมจิในการโทร", "Display the username instead of You in the Chat": "แสดงชื่อผู้ใช้แทนคุณในการแชท", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "อย่าติดตั้งฟังก์ชันจากแหล่งที่คุณไม่ไว้วางใจอย่างเต็มที่", "Do not install tools from sources you do not fully trust.": "อย่าติดตั้งเครื่องมือจากแหล่งที่คุณไม่ไว้วางใจอย่างเต็มที่", "Document": "เอกสาร", "Documentation": "เอกสารประกอบ", "Documents": "เอกสาร", "does not make any external connections, and your data stays securely on your locally hosted server.": "ไม่เชื่อมต่อภายนอกใดๆ และข้อมูลของคุณจะอยู่บนเซิร์ฟเวอร์ที่โฮสต์ในท้องถิ่นของคุณอย่างปลอดภัย", "Don't have an account?": "ยังไม่มีบัญชี?", "don't install random functions from sources you don't trust.": "อย่าติดตั้งฟังก์ชันแบบสุ่มจากแหล่งที่คุณไม่ไว้วางใจ", "don't install random tools from sources you don't trust.": "อย่าติดตั้งเครื่องมือแบบสุ่มจากแหล่งที่คุณไม่ไว้วางใจ", "Done": "เสร็จสิ้น", "Download": "ดาวน์โหลด", "Download canceled": "ยกเลิกการดาวน์โหลด", "Download Database": "ดาวน์โหลดฐานข้อมูล", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "วางไฟล์ใดๆ ที่นี่เพื่อเพิ่มในการสนทนา", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "เช่น '30s', '10m' หน่วยเวลาที่ถูกต้องคือ 's', 'm', 'h'", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "แก้ไข", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "แก้ไขความจำ", "Edit User": "แก้ไขผู้ใช้", "Edit User Group": "", "ElevenLabs": "", "Email": "อีเมล", "Embark on adventures": "", "Embedding Batch Size": "ขนาดชุดการฝัง", "Embedding Model": "โมเดลการฝัง", "Embedding Model Engine": "เครื่องยนต์โมเดลการฝัง", "Embedding model set to \"{{embedding_model}}\"": "ตั้งค่าโมเดลการฝังเป็น \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "เปิดใช้งานการแชร์ในชุมชน", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "เปิดใช้งานการสมัครใหม่", "Enable Web Search": "เปิดใช้งานการค้นหาเว็บ", "Enabled": "เปิดใช้งาน", "Engine": "เครื่องยนต์", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "ตรวจสอบว่าไฟล์ CSV ของคุณมี 4 คอลัมน์ในลำดับนี้: ชื่อ, อีเมล, รหัสผ่าน, บทบาท", "Enter {{role}} message here": "ใส่ข้อความ {{role}} ที่นี่", "Enter a detail about yourself for your LLMs to recall": "ใส่รายละเอียดเกี่ยวกับตัวคุณสำหรับ LLMs ของคุณให้จดจำ", "Enter api auth string (e.g. username:password)": "ใส่สตริงการตรวจสอบ API (เช่น username:password)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "ใส่คีย์ API ของ Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "ใส่การทับซ้อนส่วนข้อมูล", "Enter Chunk Size": "ใส่ขนาดส่วนข้อมูล", "Enter description": "", "Enter Github Raw URL": "ใส่ URL ดิบของ Github", "Enter Google PSE API Key": "ใส่คีย์ API ของ Google PSE", "Enter Google PSE Engine Id": "ใส่รหัสเครื่องยนต์ของ Google PSE", "Enter Image Size (e.g. 512x512)": "ใส่ขนาดภาพ (เช่น 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "ใส่รหัสภาษา", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "ใส่แท็กโมเดล (เช่น {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "ใส่จำนวนขั้นตอน (เช่น 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "ใส่คะแนน", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "ใส URL การค้นหาของ Searxng", "Enter Seed": "", "Enter Serper API Key": "ใส่คีย์ API ของ Serper", "Enter Serply API Key": "ใส่คีย์ API ของ Serply", "Enter Serpstack API Key": "ใส่คีย์ API ของ Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "ใส่ลำดับหยุด", "Enter system prompt": "ใส่พรอมต์ระบบ", "Enter Tavily API Key": "ใส่คีย์ API ของ Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "ใส่ URL เซิร์ฟเวอร์ของ Tika", "Enter Top K": "ใส่ Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "ใส่ URL (เช่น http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "ใส่ URL (เช่น http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "ใส่อีเมลของคุณ", "Enter Your Full Name": "ใส่ชื่อเต็มของคุณ", "Enter your message": "ใส่ข้อความของคุณ", "Enter your new password": "", "Enter Your Password": "ใส่รหัสผ่านของคุณ", "Enter your prompt": "", "Enter Your Role": "ใส่บทบาทของคุณ", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "ข้อผิดพลาด", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "การทดลอง", "Explore the cosmos": "", "Export": "ส่งออก", "Export All Archived Chats": "", "Export All Chats (All Users)": "ส่งออกการสนทนาทั้งหมด (ผู้ใช้ทั้งหมด)", "Export chat (.json)": "ส่งออกการสนทนา (.json)", "Export Chats": "ส่งออกการสนทนา", "Export Config to JSON File": "", "Export Functions": "ส่งออกฟังก์ชัน", "Export Models": "ส่งออกโมเดล", "Export Presets": "", "Export Prompts": "ส่งออกพรอมต์", "Export to CSV": "", "Export Tools": "ส่งออกเครื่องมือ", "External Models": "โมเดลภายนอก", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "สร้างคีย์ API ล้มเหลว", "Failed to read clipboard contents": "อ่านเนื้อหาคลิปบอร์ดล้มเหลว", "Failed to save models configuration": "", "Failed to update settings": "อัปเดตการตั้งค่าล้มเหลว", "February": "กุมภาพันธ์", "Feedback History": "", "Feedbacks": "", "File": "ไฟล์", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "โหมดไฟล์", "File not found.": "ไม่พบไฟล์", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "ไฟล์", "Filter is now globally disabled": "การกรองถูกปิดใช้งานทั่วโลกแล้ว", "Filter is now globally enabled": "การกรองถูกเปิดใช้งานทั่วโลกแล้ว", "Filters": "ตัวกรอง", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "ตรวจพบการปลอมแปลงลายนิ้วมือ: ไม่สามารถใช้ชื่อย่อเป็นอวตารได้ ใช้รูปโปรไฟล์เริ่มต้น", "Fluidly stream large external response chunks": "สตรีมชิ้นส่วนการตอบสนองขนาดใหญ่จากภายนอกอย่างลื่นไหล", "Focus chat input": "โฟกัสการป้อนแชท", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "ฟอร์ม", "Format your variables using brackets like this:": "", "Frequency Penalty": "การลงโทษความถี่", "Function": "", "Function created successfully": "สร้างฟังก์ชันสำเร็จ", "Function deleted successfully": "ลบฟังก์ชันสำเร็จ", "Function Description": "", "Function ID": "", "Function is now globally disabled": "ฟังก์ชันถูกปิดใช้งานทั่วโลกแล้ว", "Function is now globally enabled": "ฟังก์ชันถูกเปิดใช้งานทั่วโลกแล้ว", "Function Name": "", "Function updated successfully": "อัปเดตฟังก์ชันสำเร็จ", "Functions": "ฟังก์ชัน", "Functions allow arbitrary code execution": "ฟังก์ชันอนุญาตการเรียกใช้โค้ดโดยพลการ", "Functions allow arbitrary code execution.": "ฟังก์ชันอนุญาตการเรียกใช้โค้ดโดยพลการ", "Functions imported successfully": "นำเข้าฟังก์ชันสำเร็จ", "General": "ทั่วไป", "General Settings": "การตั้งค่าทั่วไป", "Generate Image": "สร้างภาพ", "Generating search query": "สร้างคำค้นหา", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "ทั่วโลก", "Good Response": "การตอบสนองที่ดี", "Google Drive": "", "Google PSE API Key": "คีย์ API ของ Google PSE", "Google PSE Engine Id": "รหัสเครื่องยนต์ของ Google PSE", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "ไม่มีการสนทนา", "Hello, {{name}}": "สวัสดี, {{name}}", "Help": "ช่วยเหลือ", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "ซ่อน", "Host": "", "How can I help you today?": "วันนี้ฉันจะช่วยอะไรคุณได้บ้าง?", "How would you rate this response?": "", "Hybrid Search": "การค้นหาแบบไฮบริด", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "ฉันรับทราบว่าฉันได้อ่านและเข้าใจผลกระทบของการกระทำของฉัน ฉันทราบถึงความเสี่ยงที่เกี่ยวข้องกับการเรียกใช้โค้ดโดยพลการและฉันได้ตรวจสอบความน่าเชื่อถือของแหล่งที่มาแล้ว", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "การสร้างภาพ (การทดลอง)", "Image Generation Engine": "เครื่องยนต์การสร้างภาพ", "Image Max Compression Size": "", "Image Settings": "การตั้งค่าภาพ", "Images": "ภาพ", "Import Chats": "นำเข้าการสนทนา", "Import Config from JSON File": "", "Import Functions": "นำเข้าฟังก์ชัน", "Import Models": "นำเข้าโมเดล", "Import Presets": "", "Import Prompts": "นำเข้าพรอมต์", "Import Tools": "นำเข้าเครื่องมือ", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "รวมแฟลก `--api-auth` เมื่อเรียกใช้ stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "รวมแฟลก `--api` เมื่อเรียกใช้ stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "ข้อมูล", "Input commands": "คำสั่งป้อนข้อมูล", "Install from Github URL": "ติดตั้งจาก URL ของ Github", "Instant Auto-Send After Voice Transcription": "ส่งอัตโนมัติทันทีหลังจากการถอดเสียง", "Interface": "อินเทอร์เฟซ", "Invalid file format.": "", "Invalid Tag": "แท็กไม่ถูกต้อง", "is typing...": "", "January": "มกราคม", "Jina API Key": "", "join our Discord for help.": "เข้าร่วม Discord ของเราเพื่อขอความช่วยเหลือ", "JSON": "JSON", "JSON Preview": "ดูตัวอย่าง JSON", "July": "กรกฎาคม", "June": "มิถุนายน", "JWT Expiration": "การหมดอายุของ JWT", "JWT Token": "โทเค็น JWT", "Kagi Search API Key": "", "Keep Alive": "คงอยู่", "Key": "", "Keyboard shortcuts": "ทางลัดแป้นพิมพ์", "Knowledge": "ความรู้", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "ภาษา", "Last Active": "ใช้งานล่าสุด", "Last Modified": "แก้ไขล่าสุด", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "แสง", "Listening...": "กำลังฟัง...", "Local": "", "Local Models": "โมเดลท้องถิ่น", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "สร้างโดยชุมชน OpenWebUI", "Make sure to enclose them with": "", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "จัดการ", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "จัดการไปป์ไลน์", "March": "มีนาคม", "Max Tokens (num_predict)": "โทเค็นสูงสุด (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "สามารถดาวน์โหลดโมเดลได้สูงสุด 3 โมเดลในเวลาเดียวกัน โปรดลองอีกครั้งในภายหลัง", "May": "พฤษภาคม", "Memories accessible by LLMs will be shown here.": "", "Memory": "ความจำ", "Memory added successfully": "เพิ่มโมเดลสำเร็จ", "Memory cleared successfully": "ล้าง", "Memory deleted successfully": "ลบโมเดลสำเร็จ", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "ข้อความที่คุณส่งหลังจากสร้างลิงก์ของคุณแล้วจะไม่ถูกแชร์ ผู้ใช้ที่มี URL จะสามารถดูแชทที่แชร์ได้", "Min P": "", "Minimum Score": "คะแนนขั้นต่ำ", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY hh:mm:ss A", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "โมเดล '{{modelName}}' ถูกดาวน์โหลดเรียบร้อยแล้ว", "Model '{{modelTag}}' is already in queue for downloading.": "โมเดล '{{modelTag}}' กำลังอยู่ในคิวสำหรับการดาวน์โหลด", "Model {{modelId}} not found": "ไม่พบโมเดล {{modelId}}", "Model {{modelName}} is not vision capable": "โมเดล {{modelName}} ไม่มีคุณสมบัติวิสชั่น", "Model {{name}} is now {{status}}": "โมเดล {{name}} ขณะนี้ {{status}}", "Model accepts image inputs": "", "Model created successfully!": "สร้างโมเดลสำเร็จ!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "ตรวจพบเส้นทางระบบไฟล์ของโมเดล ต้องการชื่อย่อของโมเดลสำหรับการอัปเดต ไม่สามารถดำเนินการต่อได้", "Model Filtering": "", "Model ID": "รหัสโมเดล", "Model IDs": "", "Model Name": "", "Model not selected": "ยังไม่ได้เลือกโมเดล", "Model Params": "พารามิเตอร์ของโมเดล", "Model Permissions": "", "Model updated successfully": "อัปเดตโมเดลเรียบร้อยแล้ว", "Modelfile Content": "เนื้อหาของไฟล์โมเดล", "Models": "โมเดล", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "เพิ่มเติม", "Name": "ชื่อ", "Name your knowledge base": "", "New Chat": "แชทใหม่", "New folder": "", "New Password": "รหัสผ่านใหม่", "new-channel": "", "No content found": "", "No content to speak": "ไม่มีเนื้อหาที่จะพูด", "No distance available": "", "No feedbacks found": "", "No file selected": "ไม่ได้เลือกไฟล์", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "ไม่มีผลลัพธ์", "No search query generated": "ไม่มีการสร้างคำค้นหา", "No source available": "ไม่มีแหล่งข้อมูล", "No users were found.": "", "No valves to update": "ไม่มีวาล์วที่จะอัปเดต", "None": "ไม่มี", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "หมายเหตุ: หากคุณตั้งค่าคะแนนขั้นต่ำ การค้นหาจะคืนเอกสารที่มีคะแนนมากกว่าหรือเท่ากับคะแนนขั้นต่ำเท่านั้น", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "การแจ้งเตือน", "November": "พฤศจิกายน", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "ตุลาคม", "Off": "ปิด", "Okay, Let's Go!": "ตกลง ไปกัน!", "OLED Dark": "OLED โหมดมื", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "ปิด Ollama API", "Ollama API settings updated": "", "Ollama Version": "เวอร์ชั่น <PERSON><PERSON>ma", "On": "เปิด", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "อนุญาตให้ใช้เฉพาะอักขระตัวอักษรและตัวเลข รวมถึงเครื่องหมายขีดกลางในสตริงคำสั่งเท่านั้น", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "อุ๊บส์! ดูเหมือนว่า URL ไม่ถูกต้อง กรุณาตรวจสอบและลองใหม่อีกครั้ง", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "อุ๊บส์! คุณกำลังใช้วิธีที่ไม่รองรับ (เฉพาะเว็บส่วนหน้า) กรุณาให้บริการ WebUI จากเว็บส่วนแบ็กเอนด์", "Open in full screen": "", "Open new chat": "เปิดแชทใหม่", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "เวอร์ชั่น Open WebUI (v{{OPEN_WEBUI_VERSION}}) ต่ำกว่าเวอร์ชั่นที่ต้องการ (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "การตั้งค่า OpenAI API", "OpenAI API Key is required.": "จำเป็นต้องใช้คีย์ OpenAI API", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "จำเป็นต้องใช้ URL/คีย์ OpenAI", "or": "หรือ", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "รหัสผ่าน", "Paste Large Text as File": "", "PDF document (.pdf)": "เอกสาร PDF (.pdf)", "PDF Extract Images (OCR)": "การแยกรูปภาพจาก PDF (OCR)", "pending": "รอดำเนินการ", "Permission denied when accessing media devices": "ถูกปฏิเสธเมื่อเข้าถึงอุปกรณ์", "Permission denied when accessing microphone": "ถูกปฏิเสธเมื่อเข้าถึงไมโครโฟน", "Permission denied when accessing microphone: {{error}}": "การอนุญาตถูกปฏิเสธเมื่อเข้าถึงไมโครโฟน: {{error}}", "Permissions": "", "Personalization": "การปรับแต่ง", "Pin": "ปักหมุด", "Pinned": "ปักหมุดแล้ว", "Pioneer insights": "", "Pipeline deleted successfully": "ลบไปป์ไลน์เรียบร้อยแล้ว", "Pipeline downloaded successfully": "ดาวน์โหลดไปป์ไลน์เรียบร้อยแล้ว", "Pipelines": "ไปป์ไลน์", "Pipelines Not Detected": "ไม่พบไปป์ไลน์", "Pipelines Valves": "วาล์วของไปป์ไลน์", "Plain text (.txt)": "ไฟล์ข้อความ (.txt)", "Playground": "สนามทดสอบ", "Please carefully review the following warnings:": "โปรดตรวจสอบคำเตือนต่อไปนี้อย่างละเอียด:", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "30 วันที่ผ่านมา", "Previous 7 days": "7 วันที่ผ่านมา", "Profile Image": "รูปโปรไฟล์", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "พรอมต์ (เช่น บอกข้อเท็จจริงที่น่าสนุกเกี่ยวกับจักรวรรดิโรมัน)", "Prompt Content": "เนื้อหาพรอมต์", "Prompt created successfully": "", "Prompt suggestions": "", "Prompt updated successfully": "", "Prompts": "พรอมต์", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "", "Pull a model from Ollama.com": "", "Query Generation Prompt": "", "Query Params": "พารามิเตอร์การค้นหา", "RAG Template": "แม่แบบ RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "อ่านออกเสียง", "Record voice": "บันทึกเสียง", "Redirecting you to OpenWebUI Community": "กำลังเปลี่ยนเส้นทางคุณไปยังชุมชน OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "เรียกตัวเองว่า \"ผู้ใช้\" (เช่น \"ผู้ใช้กำลังเรียนภาษาสเปน\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "สร้างใหม่", "Release Notes": "บันทึกรุ่น", "Relevance": "", "Remove": "ลบ", "Remove Model": "ลบโมเดล", "Rename": "เปลี่ยนชื่อ", "Reorder Models": "", "Repeat Last N": "ทำซ้ำครั้งล่าสุด N", "Reply in Thread": "", "Request Mode": "โหมดคำขอ", "Reranking Model": "จัดอันดับใหม่โมเดล", "Reranking model disabled": "ปิดการใช้งานโมเดลการจัดอันดับใหม่", "Reranking model set to \"{{reranking_model}}\"": "ตั้งค่าโมเดลการจัดอันดับใหม่เป็น \"{{reranking_model}}\"", "Reset": "รีเซ็ต", "Reset All Models": "", "Reset Upload Directory": "รีเซ็ตไดเร็กทอรีการอัปโหลด", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "ไม่สามารถเปิดการแจ้งเตือนการตอบสนองได้เนื่องจากเว็บไซต์ปฏิเสธ กรุณาเข้าการตั้งค่าเบราว์เซอร์ของคุณเพื่อให้สิทธิ์การเข้าถึงที่จำเป็น", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "บทบาท", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "กำลังทำงาน", "Save": "บันทึก", "Save & Create": "บันทึกและสร้าง", "Save & Update": "บันทึกและอัปเดต", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "การบันทึกบันทึกการสนทนาโดยตรงไปยังที่จัดเก็บในเบราว์เซอร์ของคุณไม่ได้รับการสนับสนุนอีกต่อไป โปรดสละเวลาสักครู่เพื่อดาวน์โหลดและลบบันทึกการสนทนาของคุณโดยคลิกที่ปุ่มด้านล่าง ไม่ต้องกังวล คุณสามารถนำเข้าบันทึกการสนทนาของคุณกลับไปยังส่วนแบ็กเอนด์ได้อย่างง่ายดายผ่าน", "Scroll to bottom when switching between branches": "", "Search": "ค้นหา", "Search a model": "ค้นหาโมเดล", "Search Base": "", "Search Chats": "ค้นหาแชท", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "ค้นหาฟังก์ชัน", "Search Knowledge": "", "Search Models": "ค้นหาโมเดล", "Search options": "", "Search Prompts": "ค้นหาพรอมต์", "Search Result Count": "จำนวนผลลัพธ์การค้นหา", "Search Tools": "เครื่องมือค้นหา", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "กำลังค้นหา \"{{searchQ<PERSON>y}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "URL คำค้นหา", "See readme.md for instructions": "ดู readme.md สำหรับคำแนะนำ", "See what's new": "ดูสิ่งที่ใหม่", "Seed": "Seed", "Select a base model": "เลือกโมเดลฐาน", "Select a engine": "เลือกเอนจิน", "Select a function": "เลือกฟังก์ชัน", "Select a group": "", "Select a model": "เลือกโมเดล", "Select a pipeline": "เลือกไปป์ไลน์", "Select a pipeline url": "เลือก URL ไปป์ไลน์", "Select a tool": "เลือกเครื่องมือ", "Select Engine": "", "Select Knowledge": "", "Select model": "เลือกโมเดล", "Select only one model to call": "เลือกเพียงโมเดลเดียวที่จะใช้", "Selected model(s) do not support image inputs": "โมเดลที่เลือกไม่รองรับภาพ", "Semantic distance to query": "", "Send": "ส่ง", "Send a message": "", "Send a Message": "ส่งข้อความ", "Send message": "ส่งข้อความ", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "กันยายน", "Serper API Key": "คีย์ API ของ Serper", "Serply API Key": "คีย์ API ของ Serply", "Serpstack API Key": "คีย์ API ของ Serpstack", "Server connection verified": "ยืนยันการเชื่อมต่อเซิร์ฟเวอร์แล้ว", "Set as default": "ตั้งเป็นค่าเริ่มต้น", "Set CFG Scale": "", "Set Default Model": "ตั้งโมเดลเริ่มต้น", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ตั้งค่าโมเดลการฝัง (เช่น {{model}})", "Set Image Size": "ตั้งค่าขนาดภาพ", "Set reranking model (e.g. {{model}})": "ตั้งค่าโมเดลการจัดอันดับใหม่ (เช่น {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "ตั้งค่าขั้นตอน", "Set Task Model": "ตั้งค่าโมเดลงาน", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "ตั้งค่าเสียง", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "การตั้งค่า", "Settings saved successfully!": "บันทึกการตั้งค่าเรียบร้อยแล้ว!", "Share": "แชร์", "Share Chat": "แชร์แชท", "Share to OpenWebUI Community": "แชร์ไปยังชุมชน OpenWebUI", "Show": "แสดง", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "แสดงรายละเอียดผู้ดูแลระบบในหน้าจอรอการอนุมัติบัญชี", "Show shortcuts": "แสดงทางลัด", "Show your support!": "แสดงการสนับสนุนของคุณ!", "Sign in": "ลงชื่อเข้าใช้", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "ลงชื่อออก", "Sign up": "สมัครสมาชิก", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "แหล่งที่มา", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "ข้อผิดพลาดในการรู้จำเสียง: {{error}}", "Speech-to-Text Engine": "เครื่องมือแปลงเสียงเป็นข้อความ", "Stop": "", "Stop Sequence": "หยุดลำดับ", "Stream Chat Response": "", "STT Model": "โมเดลแปลงเสียงเป็นข้อความ", "STT Settings": "การตั้งค่าแปลงเสียงเป็นข้อความ", "Success": "สำเร็จ", "Successfully updated.": "อัปเดตเรียบร้อยแล้ว", "Suggested prompts to get you started": "", "Support": "สนับสนุน", "Support this plugin:": "สนับสนุนปลั๊กอินนี้:", "Sync directory": "", "System": "ระบบ", "System Instructions": "", "System Prompt": "ระบบพรอมต์", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "แตะเพื่อขัดจังหวะ", "Tavily API Key": "คีย์ API ของ Tavily", "Temperature": "อุณหภูมิ", "Template": "แม่แบบ", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "เครื่องมือแปลงข้อความเป็นเสียง", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "ขอบคุณสำหรับความคิดเห็นของคุณ!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "นักพัฒนาที่อยู่เบื้องหลังปลั๊กอินนี้เป็นอาสาสมัครที่มีชื่นชอบการแบ่งบัน หากคุณพบว่าปลั๊กอินนี้มีประโยชน์ โปรดพิจารณาสนับสนุนการพัฒนาของเขา", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "คะแนนควรอยู่ระหว่าง 0.0 (0%) ถึง 1.0 (100%)", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "ธีม", "Thinking...": "กำลังคิด...", "This action cannot be undone. Do you wish to continue?": "การกระทำนี้ไม่สามารถย้อนกลับได้ คุณต้องการดำเนินการต่อหรือไม่?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "สิ่งนี้ทำให้มั่นใจได้ว่าการสนทนาที่มีค่าของคุณจะถูกบันทึกอย่างปลอดภัยในฐานข้อมูลแบ็กเอนด์ของคุณ ขอบคุณ!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "นี่เป็นฟีเจอร์ทดลอง อาจไม่ทำงานตามที่คาดไว้และอาจมีการเปลี่ยนแปลงได้ตลอดเวลา", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "สิ่งนี้จะลบ", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "จำเป็นต้องมี URL ของเซิร์ฟเวอร์ Tika", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "เคล็ดลับ: อัปเดตช่องตัวแปรหลายช่องติดต่อกันโดยการกดปุ่มแท็บในช่องใส่ข้อความแชทหลังจากแต่ละการแทนที่", "Title": "ชื่อเรื่อง", "Title (e.g. Tell me a fun fact)": "ชื่อเรื่อง (เช่น บอกข้อเท็จจริงที่น่าสนุก)", "Title Auto-Generation": "การสร้างชื่ออัตโนมัติ", "Title cannot be an empty string.": "ชื่อเรื่องไม่สามารถเป็นสตริงว่างได้", "Title Generation Prompt": "พรอมต์การสร้างชื่อเรื่อง", "TLS": "", "To access the available model names for downloading,": "ในการเข้าถึงชื่อโมเดลที่มีให้ดาวน์โหลด", "To access the GGUF models available for downloading,": "ในการเข้าถึงโมเดล GGUF ที่มีให้ดาวน์โหลด", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "ในการเข้าถึง WebUI โปรดติดต่อผู้ดูแลระบบ ผู้ดูแลระบบสามารถจัดการสถานะผู้ใช้จากแผงควบคุมผู้ดูแลระบบ", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "ในการเลือกฟิลเตอร์ที่นี่ ให้เพิ่มไปยังพื้นที่ทำงาน \"ฟังก์ชัน\" ก่อน", "To select toolkits here, add them to the \"Tools\" workspace first.": "ในการเลือกชุดเครื่องมือที่นี่ ให้เพิ่มไปยังพื้นที่ทำงาน \"เครื่องมือ\" ก่อน", "Toast notifications for new updates": "", "Today": "วันนี้", "Toggle settings": "สลับการตั้งค่า", "Toggle sidebar": "สลับแถบด้านข้าง", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "โทเค็นที่เก็บไว้เมื่อรีเฟรชบริบท (num_keep)", "Tool created successfully": "สร้างเครื่องมือเรียบร้อยแล้ว", "Tool deleted successfully": "ลบเครื่องมือเรียบร้อยแล้ว", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "นำเข้าเครื่องมือเรียบร้อยแล้ว", "Tool Name": "", "Tool updated successfully": "อัปเดตเครื่องมือเรียบร้อยแล้ว", "Tools": "เครื่องมือ", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "เครื่องมือคือระบบการเรียกใช้ฟังก์ชันที่สามารถดำเนินการโค้ดใดๆ ได้", "Tools have a function calling system that allows arbitrary code execution": "เครื่องมือมีระบบการเรียกใช้ฟังก์ชันที่สามารถดำเนินการโค้ดใดๆ ได้", "Tools have a function calling system that allows arbitrary code execution.": "เครื่องมือมีระบบการเรียกใช้ฟังก์ชันที่สามารถดำเนินการโค้ดใดๆ ได้", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "มีปัญหาในการเข้าถึง Ollama?", "TTS Model": "โมเดลแปลงข้อความเป็นเสียง", "TTS Settings": "การตั้งค่าแปลงข้อความเป็นเสียง", "TTS Voice": "เสียงแปลงข้อความเป็นเสียง", "Type": "ประเภท", "Type Hugging Face Resolve (Download) URL": "พิมพ์ URL ของ Hugging Face Resolve (Download)", "Uh-oh! There was an issue with the response.": "", "UI": "ส่วนติดต่อผู้ใช้", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "ยกเลิกการปักหมุด", "Unravel secrets": "", "Untagged": "", "Update": "อัปเดต", "Update and Copy Link": "อัปเดตและคัดลอกลิงก์", "Update for the latest features and improvements.": "", "Update password": "อัปเดตรหัสผ่าน", "Updated": "", "Updated at": "อัปเดตเมื่อ", "Updated At": "", "Upload": "อัปโหลด", "Upload a GGUF model": "อัปโหลดโมเดล GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "อัปโหลดไฟล์", "Upload Pipeline": "อัปโหลดพายป์ไลน์", "Upload Progress": "ความคืบหน้าการอัปโหลด", "URL": "", "URL Mode": "โหมด URL", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "ใช้ Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "ใช้ตัวย่อ", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "ผู้ใช้", "User": "", "User location successfully retrieved.": "ดึงตำแหน่งที่ตั้งของผู้ใช้เรียบร้อยแล้ว", "Username": "", "Users": "ผู้ใช้", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "ใช้", "Valid time units:": "หน่วยเวลาใช้ได้:", "Valves": "วาล์ว", "Valves updated": "วาล์วที่อัปเดตแล้ว", "Valves updated successfully": "อัปเดตวาล์วเรียบร้อยแล้ว", "variable": "ตัวแปร", "variable to have them replaced with clipboard content.": "ตัวแปรเพื่อให้แทนที่ด้วยเนื้อหาคลิปบอร์ด", "Version": "เวอร์ชัน", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "เสียง", "Voice Input": "", "Warning": "คำเตือน", "Warning:": "คำเตือน:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "คำเตือน: หากคุณอัปเดตหรือเปลี่ยนโมเดลการฝัง คุณจะต้องนำเข้าเอกสารทั้งหมดอีกครั้ง", "Web": "เว็บ", "Web API": "เว็บ API", "Web Loader Settings": "การตั้งค่าเว็บโหลดเดอร์", "Web Search": "การค้นหาเว็บ", "Web Search Engine": "เครื่องมือค้นหาเว็บ", "Web Search Query Generation": "", "Webhook URL": "URL ของ Webhook", "WebUI Settings": "การตั้งค่า WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "มีอะไรใหม่ใน", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "<PERSON>his<PERSON> (โลคอล)", "Widescreen Mode": "โหมดหน้าจอกว้าง", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "พื้นที่ทำงาน", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "เขียนคำแนะนำพรอมต์ (เช่น คุณคือใคร?)", "Write a summary in 50 words that summarizes [topic or keyword].": "เขียนสรุปใน 50 คำที่สรุป [หัวข้อหรือคำสำคัญ]", "Write something...": "", "Write your model template content here": "", "Yesterday": "เมื่อวาน", "You": "คุณ", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "คุณสามารถปรับแต่งการโต้ตอบของคุณกับ LLMs โดยเพิ่มความทรงจำผ่านปุ่ม 'จัดการ' ด้านล่าง ทำให้มันมีประโยชน์และเหมาะกับคุณมากขึ้น", "You cannot upload an empty file.": "", "You have no archived conversations.": "คุณไม่มีการสนทนาที่เก็บถาวร", "You have shared this chat": "คุณได้แชร์แชทนี้แล้ว", "You're a helpful assistant.": "คุณคือผู้ช่วยที่มีประโยชน์", "Your account status is currently pending activation.": "สถานะบัญชีของคุณกำลังรอการเปิดใช้งาน", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "การสนับสนุนทั้งหมดของคุณจะไปยังนักพัฒนาปลั๊กอินโดยตรง; Open WebUI ไม่รับส่วนแบ่งใด ๆ อย่างไรก็ตาม แพลตฟอร์มการระดมทุนที่เลือกอาจมีค่าธรรมเนียมของตัวเอง", "Youtube": "Youtube", "Youtube Loader Settings": "การตั้งค่าโหลดเดอร์ Youtube"}