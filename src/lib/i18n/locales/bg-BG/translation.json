{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' или '-1' за неограничен срок.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(например `sh webui.sh --api`)", "(latest)": "(последна)", "{{ models }}": "{{ модели }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}'s чатове", "{{webUIName}} Backend Required": "{{webUIName}} Изисква се Бекенд", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Моделът на задачите се използва при изпълнение на задачи като генериране на заглавия за чатове и заявки за търсене в мрежата", "a user": "потребител", "About": "Относно", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON>ка<PERSON><PERSON>т", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "Добавяне", "Add a model ID": "", "Add a short description about what this model does": "Добавете кратко описание за това какво прави този модел", "Add a tag": "Добавяне на таг", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "Добавяне на собствен промпт", "Add Files": "Добавяне на Файлове", "Add Group": "", "Add Memory": "Добавяне на Памет", "Add Model": "Добавяне на Модел", "Add Reaction": "", "Add Tag": "", "Add Tags": "добавяне на тагове", "Add text content": "", "Add User": "Добавяне на потребител", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "При промяна на тези настройки промените се прилагат за всички потребители.", "admin": "админ", "Admin": "", "Admin Panel": "Панел на Администратор", "Admin Settings": "Настройки на Администратор", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "Разширени Параметри", "Advanced Params": "Разширени параметри", "All Documents": "Всички Документи", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Позволи Изтриване на Чат", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "Вече имате акаунт? ", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "асистент", "and": "и", "and {{COUNT}} more": "", "and create a new shared link.": "и създай нов общ линк.", "API Base URL": "API Базов URL", "API Key": "<PERSON> Ключ", "API Key created.": "API Ключ създаден.", "API Key Endpoint Restrictions": "", "API keys": "API Ключове", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON>рил", "Archive": "Архиви<PERSON><PERSON><PERSON><PERSON> Чатове", "Archive All Chats": "Архив Всички чатове", "Archived Chats": "Архиви<PERSON><PERSON><PERSON><PERSON> Чатове", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Сигурни ли сте?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "Прикачване на файл", "Attribute for Username": "", "Audio": "Аудио", "August": "Август", "Authenticate": "", "Auto-Copy Response to Clipboard": "Аувтоматично копиране на отговор в клипборда", "Auto-playback response": "Аувтоматично възпроизвеждане на Отговора", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 Базов URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Базов URL е задължителен.", "Available list": "", "available!": "наличен!", "Azure AI Speech": "", "Azure Region": "", "Back": "Назад", "Bad": "", "Bad Response": "Невалиден отговор от API", "Banners": "Банери", "Base Model (From)": "Базов модел (от)", "Batch Size (num_batch)": "", "before": "преди", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Смел ключ за API за търсене", "By {{name}}": "", "Bypass SSL verification for Websites": "Изключване на SSL проверката за сайтове", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "Отказ", "Capabilities": "Възможности", "Capture": "", "Certificate Path": "", "Change Password": "Промяна на Парола", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Чат", "Chat Background Image": "", "Chat Bubble UI": "UI за чат бублон", "Chat Controls": "", "Chat direction": "Направление на чата", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Чатове", "Check Again": "Проверете Още Веднъж", "Check for updates": "Проверка за актуализации", "Checking for updates...": "Проверка за актуализации...", "Choose a model before saving...": "Изберете модел преди запазване...", "Chunk Overlap": "<PERSON><PERSON>", "Chunk Params": "Chunk Params", "Chunk Size": "Chunk Size", "Ciphers": "", "Citation": "Цитат", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Натиснете тук за помощ.", "Click here to": "Натиснете тук за", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Натиснете тук, за да изберете", "Click here to select a csv file.": "Натиснете тук, за да изберете csv файл.", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "натиснете тук.", "Click on the user role button to change a user's role.": "Натиснете върху бутона за промяна на ролята на потребителя.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "Клонинг", "Close": "Затвори", "Code execution": "", "Code formatted successfully": "", "Collection": "Колекция", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL е задължително.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Команда", "Completions": "", "Concurrent Requests": "Едновременни искания", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "Потвърди Парола", "Confirm your action": "", "Confirm your new password": "", "Connections": "Връзки", "Contact Admin for WebUI Access": "", "Content": "Съдържание", "Content Extraction": "", "Context Length": "Дъ<PERSON><PERSON><PERSON>на на Контекста", "Continue Response": "Продължи отговора", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "Копирана е връзката за чат!", "Copied to clipboard": "", "Copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "Copy last code block": "Копиране на последен код блок", "Copy last response": "Копиране на последен отговор", "Copy Link": "Копиране на връзка", "Copy to clipboard": "", "Copying to clipboard was successful!": "Копирането в клипборда беше успешно!", "Create": "", "Create a knowledge base": "", "Create a model": "Създаване на модел", "Create Account": "Създаване на Акаунт", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Създаване на нов ключ", "Create new secret key": "Създаване на нов секретен ключ", "Created at": "Създадено на", "Created At": "Създадено на", "Created by": "", "CSV Import": "", "Current Model": "Текущ модел", "Current Password": "Текуща Парола", "Custom": "Персонализ<PERSON><PERSON><PERSON>н", "Dark": "Тъм<PERSON>н", "Database": "База данни", "December": "Декември", "Default": "По подразбиране", "Default (Open AI)": "", "Default (SentenceTransformers)": "По подразбиране (SentenceTransformers)", "Default Model": "Модел по подразбиране", "Default model updated": "Моделът по подразбиране е обновен", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Промпт Предложения по подразбиране", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Роля на потребителя по подразбиране", "Delete": "Изтриване", "Delete a model": "Изтриване на модел", "Delete All Chats": "Изтриване на всички чатове", "Delete All Models": "", "Delete chat": "Изтриване на чат", "Delete Chat": "Изтриване на Чат", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "Изтриване на този линк", "Delete tool?": "", "Delete User": "Изтриване на потребител", "Deleted {{deleteModelTag}}": "Изтрито {{deleteModelTag}}", "Deleted {{name}}": "Изтрито {{име}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Описание", "Disabled": "", "Discover a function": "", "Discover a model": "Открийте модел", "Discover a prompt": "Откриване на промпт", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "Откриване, сваляне и преглед на персонализирани промптове", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "Откриване, сваляне и преглед на пресетове на модели", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "Показване на потребителското име вместо Вие в чата", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Документ", "Documentation": "", "Documents": "Документи", "does not make any external connections, and your data stays securely on your locally hosted server.": "няма външни връзки, и вашите данни остават сигурни на локално назначен сървър.", "Don't have an account?": "Нямате акаунт?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "Изтегляне отменено", "Download canceled": "Изтегляне отменено", "Download Database": "Сваляне на база данни", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Пускане на файлове тук, за да ги добавите в чата", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "напр. '30с','10м'. Валидни единици са 'с', 'м', 'ч'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Редактиране", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "Редактиране на потребител", "Edit User Group": "", "ElevenLabs": "", "Email": "Имейл", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "Модел за вграждане", "Embedding Model Engine": "Модел за вграждане", "Embedding model set to \"{{embedding_model}}\"": "Модел за вграждане е настроен на \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Разрешаване на споделяне в общност", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Вклюване на Нови Потребители", "Enable Web Search": "Разрешаване на търсене в уеб", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Уверете се, че вашият CSV файл включва 4 колони в следния ред: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Парола, Роля.", "Enter {{role}} message here": "Въведете съобщение за {{role}} тук", "Enter a detail about yourself for your LLMs to recall": "Въведете подробности за себе си, за да се herinnerат вашите LLMs", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Въведете Brave Search API ключ", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Въведете Chunk Overlap", "Enter Chunk Size": "Въведете Chunk Size", "Enter description": "", "Enter Github Raw URL": "Въведете URL адреса на Github Raw", "Enter Google PSE API Key": "Въведете Google PSE API ключ", "Enter Google PSE Engine Id": "Въведете идентификатор на двигателя на Google PSE", "Enter Image Size (e.g. 512x512)": "Въведете размер на изображението (напр. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Въведете кодове на езика", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Въведете таг на модел (напр. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Въведете брой стъпки (напр. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Въведете оценка", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Въведете URL адреса на заявката на Searxng", "Enter Seed": "", "Enter Serper API Key": "Въведете Serper API ключ", "Enter Serply API Key": "", "Enter Serpstack API Key": "Въведете Serpstack API ключ", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Въведете стоп последователност", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Въведете Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Въведете URL (напр. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Въведете URL (напр. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Въведете имейл", "Enter Your Full Name": "Въведете вашето пълно име", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "Въведете вашата парола", "Enter your prompt": "", "Enter Your Role": "Въведете вашата роля", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Грешка", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Експериментално", "Explore the cosmos": "", "Export": "Износ", "Export All Archived Chats": "", "Export All Chats (All Users)": "Експортване на всички чатове (За всички потребители)", "Export chat (.json)": "", "Export Chats": "Експортване на чатове", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "Експортиране на модели", "Export Presets": "", "Export Prompts": "Експортване на промптове", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Неуспешно създаване на API ключ.", "Failed to read clipboard contents": "Грешка при четене на съдържанието от клипборда", "Failed to save models configuration": "", "Failed to update settings": "", "February": "Февруари", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "Файл Мод", "File not found.": "Файл не е намерен.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Потвърждаване на отпечатък: Не може да се използва инициализационна буква като аватар. Потребителят се връща към стандартна аватарка.", "Fluidly stream large external response chunks": "Плавно предаване на големи части от външен отговор", "Focus chat input": "Фокусиране на чат вход", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "Наказание за честота", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "Основни", "General Settings": "Основни Настройки", "Generate Image": "", "Generating search query": "Генериране на заявка за търсене", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "Добра отговор", "Google Drive": "", "Google PSE API Key": "Google PSE API ключ", "Google PSE Engine Id": "Идентификатор на двигателя на Google PSE", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "няма разговори.", "Hello, {{name}}": "Здра<PERSON><PERSON><PERSON>, {{name}}", "Help": "Помощ", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Скрий", "Host": "", "How can I help you today?": "Как мога да ви помогна днес?", "How would you rate this response?": "", "Hybrid Search": "Hybrid Search", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Генерация на изображения (Експериментално)", "Image Generation Engine": "Двигател за генериране на изображения", "Image Max Compression Size": "", "Image Settings": "Настройки на изображения", "Images": "Изображения", "Import Chats": "Импортване на чатове", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "Импортиране на модели", "Import Presets": "", "Import Prompts": "Импортване на промптове", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "Включете флага `--api`, когато стартирате stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Информация", "Input commands": "Въведете команди", "Install from Github URL": "Инсталиране от URL адреса на Github", "Instant Auto-Send After Voice Transcription": "", "Interface": "Интерфейс", "Invalid file format.": "", "Invalid Tag": "Невалиден тег", "is typing...": "", "January": "Яну<PERSON><PERSON>и", "Jina API Key": "", "join our Discord for help.": "свържете се с нашия Discord за помощ.", "JSON": "JSON", "JSON Preview": "JSON Преглед", "July": "<PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON>", "JWT Expiration": "JWT Expiration", "JWT Token": "JWT Token", "Kagi Search API Key": "", "Keep Alive": "Keep Alive", "Key": "", "Keyboard shortcuts": "Клавиши за бърз достъп", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "Език", "Last Active": "Последни активни", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "Светъл", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Направено от OpenWebUI общността", "Make sure to enclose them with": "Уверете се, че са заключени с", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Управление на тръбопроводи", "March": "Ма<PERSON><PERSON>", "Max Tokens (num_predict)": "Макс токени (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Максимум 3 модели могат да бъдат сваляни едновременно. Моля, опитайте отново по-късно.", "May": "<PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Мемории достъпни от LLMs ще бъдат показани тук.", "Memory": "Мемория", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Съобщенията, които изпращате след създаването на връзката, няма да бъдат споделяни. Потребителите с URL адреса ще могат да видят споделения чат.", "Min P": "", "Minimum Score": "Мини<PERSON><PERSON><PERSON>на оценка", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Моделът '{{modelName}}' беше успешно свален.", "Model '{{modelTag}}' is already in queue for downloading.": "Моделът '{{modelTag}}' е вече в очакване за сваляне.", "Model {{modelId}} not found": "Моделът {{modelId}} не е намерен", "Model {{modelName}} is not vision capable": "Моделът {{modelName}} не може да се вижда", "Model {{name}} is now {{status}}": "Моделът {{name}} сега е {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Открит е път до файловата система на модела. За актуализацията се изисква съкратено име на модела, не може да продължи.", "Model Filtering": "", "Model ID": "ИД на модел", "Model IDs": "", "Model Name": "", "Model not selected": "Не е избран модел", "Model Params": "<PERSON><PERSON><PERSON><PERSON>л Params", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "Съдържание на модфайл", "Models": "Модели", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "Повече", "Name": "Име", "Name your knowledge base": "", "New Chat": "Нов чат", "New folder": "", "New Password": "Нова парола", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Няма намерени резултати", "No search query generated": "Не е генерирана заявка за търсене", "No source available": "Няма наличен източник", "No users were found.": "", "No valves to update": "", "None": "Никой", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Забележка: Ако зададете минимален резултат, търсенето ще върне само документи с резултат, по-голям или равен на минималния резултат.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Десктоп Известия", "November": "Ноември", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "", "October": "Октомври", "Off": "Изкл.", "Okay, Let's Go!": "ОК, Нека започваме!", "OLED Dark": "OLED тъмно", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API деактивиран", "Ollama API settings updated": "", "Ollama Version": "Ollama Версия", "On": "<PERSON><PERSON><PERSON>.", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Само алфанумерични знаци и тире са разрешени в командния низ.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Упс! Изглежда URL адресът е невалиден. Моля, проверете отново и опитайте пак.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Упс! Използвате неподдържан метод (само фронтенд). Моля, сервирайте WebUI от бекенда.", "Open in full screen": "", "Open new chat": "Отвори нов чат", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API Config", "OpenAI API Key is required.": "OpenAI API ключ е задължителен.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/Key е задължителен.", "or": "или", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "Парола", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF документ (.pdf)", "PDF Extract Images (OCR)": "PDF Extract Images (OCR)", "pending": "в очакване", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "Permission denied when accessing microphone: {{error}}", "Permissions": "", "Personalization": "Персонализация", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "Тръбопроводи", "Pipelines Not Detected": "", "Pipelines Valves": "Тръбопроводи Вентили", "Plain text (.txt)": "Plain text (.txt)", "Playground": "Пле<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>д", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Предыдущите 30 дни", "Previous 7 days": "Предыдущите 7 дни", "Profile Image": "Профилна снимка", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Промпт (напр. Обмисли ме забавна факт за Римската империя)", "Prompt Content": "Съдържание на промпта", "Prompt created successfully": "", "Prompt suggestions": "Промпт предложения", "Prompt updated successfully": "", "Prompts": "Промптове", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Извади \"{{searchValue}}\" от Ollama.com", "Pull a model from Ollama.com": "Издърпайте модел от Ollama.com", "Query Generation Prompt": "", "Query Params": "Query Параметри", "RAG Template": "RAG Шаблон", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "Прочети на Голос", "Record voice": "Записване на глас", "Redirecting you to OpenWebUI Community": "Пренасочване към OpenWebUI общността", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "Регенериране", "Release Notes": "Бележки по изданието", "Relevance": "", "Remove": "Изтриване", "Remove Model": "Изтриване на модела", "Rename": "Преименуване", "Reorder Models": "", "Repeat Last N": "Repeat Last N", "Reply in Thread": "", "Request Mode": "Request Mode", "Reranking Model": "Reranking Model", "Reranking model disabled": "Reranking model disabled", "Reranking model set to \"{{reranking_model}}\"": "Reranking model set to \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Роля", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "", "Save": "<PERSON>а<PERSON><PERSON><PERSON>", "Save & Create": "Запис & Създаване", "Save & Update": "Запис & Актуализиране", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Запазването на чат логове директно в хранилището на вашия браузър вече не се поддържа. Моля, отделете малко време, за да изтеглите и изтриете чат логовете си, като щракнете върху бутона по-долу. Не се притеснявайте, можете лесно да импортирате отново чат логовете си в бекенда чрез", "Scroll to bottom when switching between branches": "", "Search": "Търси", "Search a model": "Търси модел", "Search Base": "", "Search Chats": "Търсене на чатове", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "Търсене на модели", "Search options": "", "Search Prompts": "Търси Промптове", "Search Result Count": "Брой резултати от търсенето", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "URL адрес на заявка на Searxng", "See readme.md for instructions": "Виж readme.md за инструкции", "See what's new": "Виж какво е новото", "Seed": "Seed", "Select a base model": "Изберете базов модел", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "Изберете модел", "Select a pipeline": "Изберете тръбопровод", "Select a pipeline url": "Избор на URL адрес на канал", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "Изберете модел", "Select only one model to call": "", "Selected model(s) do not support image inputs": "Избраният(те) модел(и) не поддържа въвеждане на изображения", "Semantic distance to query": "", "Send": "Изпрати", "Send a message": "", "Send a Message": "Изпращане на Съобщение", "Send message": "Изпращане на съобщение", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Септември", "Serper API Key": "Serper API ключ", "Serply API Key": "", "Serpstack API Key": "Serpstack API ключ", "Server connection verified": "Server connection verified", "Set as default": "Задай по подразбиране", "Set CFG Scale": "", "Set Default Model": "Задай Модел По Подразбиране", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Задай embedding model (e.g. {{model}})", "Set Image Size": "Задай Размер на Изображението", "Set reranking model (e.g. {{model}})": "Задай reranking model (e.g. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Задай Стъпки", "Set Task Model": "Задаване на модел на задача", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON><PERSON><PERSON><PERSON>лас", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Настройки", "Settings saved successfully!": "Настройките са запазени успешно!", "Share": "Подели", "Share Chat": "Подели Чат", "Share to OpenWebUI Community": "Споделите с OpenWebUI Общността", "Show": "Пока<PERSON>и", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "Пока<PERSON>и", "Show your support!": "", "Sign in": "Вписване", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Изход", "Sign up": "Регистрация", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Източник", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Speech recognition error: {{error}}", "Speech-to-Text Engine": "Speech-to-Text Engine", "Stop": "", "Stop Sequence": "Stop Sequence", "Stream Chat Response": "", "STT Model": "", "STT Settings": "STT Настройки", "Success": "Успех", "Successfully updated.": "Успешно обновено.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "Система", "System Instructions": "", "System Prompt": "Системен Промпт", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "Температура", "Template": "Шабл<PERSON>н", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "Text-to-Speech Engine", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Благодарим ви за вашия отзив!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "The score should be a value between 0.0 (0%) and 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "Тема", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Това гарантира, че ценните ви разговори се запазват сигурно във вашата бекенд база данни. Благодарим ви!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Съвет: Актуализирайте няколко слота за променливи последователно, като натискате клавиша Tab в чат входа след всяка подмяна.", "Title": "Заглавие", "Title (e.g. Tell me a fun fact)": "Заглавие (напр. Моля, кажете ми нещо забавно)", "Title Auto-Generation": "Автоматично Генериране на Заглавие", "Title cannot be an empty string.": "Заглавието не може да бъде празно.", "Title Generation Prompt": "Промпт за Генериране на Заглавие", "TLS": "", "To access the available model names for downloading,": "За да получите достъп до наличните имена на модели за изтегляне,", "To access the GGUF models available for downloading,": "За да получите достъп до GGUF моделите, налични за изтегляне,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "д<PERSON><PERSON><PERSON>", "Toggle settings": "Toggle settings", "Toggle sidebar": "Toggle sidebar", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "Проблеми с достъпът до Ollama?", "TTS Model": "", "TTS Settings": "TTS Настройки", "TTS Voice": "", "Type": "Вид", "Type Hugging Face Resolve (Download) URL": "Въведете Hugging Face Resolve (Download) URL", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "Обнови и копирай връзка", "Update for the latest features and improvements.": "", "Update password": "Обновяване на парола", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "Качване на GGUF модел", "Upload directory": "", "Upload files": "", "Upload Files": "Качване на файлове", "Upload Pipeline": "", "Upload Progress": "Прогрес на качването", "URL": "", "URL Mode": "URL Mode", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "Използвайте Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "Използвайте Инициали", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "потребител", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "Потребители", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "Използване", "Valid time units:": "Валидни единици за време:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "променлива", "variable to have them replaced with clipboard content.": "променливи да се заменят съдържанието от клипборд.", "Version": "Версия", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "Предупреждение", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Предупреждение: Ако актуализирате или промените вашия модел за вграждане, трябва да повторите импортирането на всички документи.", "Web": "Уеб", "Web API": "", "Web Loader Settings": "Настройки за зареждане на уеб", "Web Search": "Търсене в уеб", "Web Search Engine": "Уеб търсачка", "Web Search Query Generation": "", "Webhook URL": "Уебхук URL", "WebUI Settings": "WebUI Настройки", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Какво е новото в", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Работно пространство", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Напиши предложение за промпт (напр. Кой сте вие?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Напиши описание в 50 знака, което описва [тема или ключова дума].", "Write something...": "", "Write your model template content here": "", "Yesterday": "вчера", "You": "вие", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "Нямате архивирани разговори.", "You have shared this chat": "Вие сте споделели този чат", "You're a helpful assistant.": "Вие сте полезен асистент.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "Youtube Loader Settings"}