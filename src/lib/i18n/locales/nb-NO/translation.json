{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 't', 'd', 'u' eller '-1' for ingen u<PERSON><PERSON><PERSON>.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(f.eks. `sh webui.sh --api --api-auth brukernavn_passord`)", "(e.g. `sh webui.sh --api`)": "(f.eks. `sh webui.sh --api`)", "(latest)": "(siste)", "{{ models }}": "{{ modeller }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} sine samtaler", "{{webUIName}} Backend Required": "Backend til {{webUIName}} kreves", "*Prompt node ID(s) are required for image generation": "Node-ID-er for ledetekst kreves for generering av bilder", "A new version (v{{LATEST_VERSION}}) is now available.": "En ny versjon (v{{LATEST_VERSION}}) er nå tilgjengelig.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "En oppgavemodell brukes når du utfører oppgaver som å generere titler for samtaler eller utfører søkeforespørsler på nettet", "a user": "en bruker", "About": "Om", "Access": "<PERSON><PERSON><PERSON><PERSON>", "Access Control": "Tilgangskontroll", "Accessible to all users": "Tilgjengelig for alle brukere", "Account": "Ko<PERSON>", "Account Activation Pending": "Venter på kontoaktivering", "Actions": "<PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktiver denne kommandoen ved å skrive inn \"/{{COMMAND}}\" i chattens inntastingsfelt.", "Active Users": "Aktive brukere", "Add": "Legg til", "Add a model ID": "Legg til en modell-ID", "Add a short description about what this model does": "Legg til en kort beskrivelse av hva denne modellen gjør", "Add a tag": "Legg til en tag", "Add Arena Model": "<PERSON><PERSON>-modell", "Add Connection": "<PERSON>gg til tilkobling", "Add Content": "Legg til innhold", "Add content here": "Legg til innhold her", "Add custom prompt": "Legg til tilpasset ledetekst", "Add Files": "Legg til filer", "Add Group": "Legg til gruppe", "Add Memory": "Legg til minne", "Add Model": "Legg til modell", "Add Reaction": "", "Add Tag": "<PERSON><PERSON> til etikett", "Add Tags": "<PERSON><PERSON> til etiketter", "Add text content": "Legg til tekstinnhold", "Add User": "Legg til bruker", "Add User Group": "<PERSON>gg til brukergruppe", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Endring av disse innstillingene vil gjelde for alle brukere på tvers av systemet.", "admin": "administrator", "Admin": "Administrator", "Admin Panel": "<PERSON><PERSON><PERSON>", "Admin Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "<PERSON><PERSON> har alltid tilgang til alle verktøy. Brukere må få tildelt verktøy per modell i arbeidsområdet.", "Advanced Parameters": "Avanserte parametere", "Advanced Params": "Avanserte parametere", "All Documents": "Alle dokumenter", "All models deleted successfully": "Alle modeller er slettet", "Allow Chat Delete": "Tillat sletting av chatter", "Allow Chat Deletion": "Tillat sletting av chatter", "Allow Chat Edit": "Tillat redigering av chatter", "Allow File Upload": "Tillatt opplasting av filer", "Allow non-local voices": "Tillat ikke-lokale stemmer", "Allow Temporary Chat": "Tillat midlertidige chatter", "Allow User Location": "Aktiver stedstjenester", "Allow Voice Interruption in Call": "Mu<PERSON><PERSON><PERSON><PERSON><PERSON>avbrytel<PERSON> i samtaler", "Allowed Endpoints": "", "Already have an account?": "Har du allerede en konto?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "Alternativ til top_p, og har som mål å sikre en balanse mellom kvalitet og variasjon. Parameteren p representerer minimumssannsynligheten for at et token skal vurderes, i forhold til sannsynligheten for det mest sannsynlige tokenet. Hvis p for eksempel er 0,05 og det mest sannsynlige tokenet har en sannsynlighet på 0,9, filtreres logits med en verdi på mindre enn 0,045 bort. (Standard: 0,0)", "an assistant": "en assistent", "and": "og", "and {{COUNT}} more": "og {{COUNT}} til", "and create a new shared link.": "og opprett en ny delt lenke.", "API Base URL": "Absolutt API-URL", "API Key": "API-nøkkel", "API Key created.": "API-nøkkel opprettet.", "API Key Endpoint Restrictions": "", "API keys": "API-nøkler", "Application DN": "Applikasjonens DN", "Application DN Password": "Applikasjonens DN-passord", "applies to all users with the \"user\" role": "gjelder for alle brukere med rollen \"user\"", "April": "april", "Archive": "Arkiv", "Archive All Chats": "<PERSON><PERSON> alle chatter", "Archived Chats": "Arkiverte chatter", "archived-chat-export": "archived-chat-export", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "Er du sikker på at du vil oppheve arkiveringen av alle arkiverte chatter?", "Are you sure?": "<PERSON>r du sikker?", "Arena Models": "Arena-modeller", "Artifacts": "Artifakter", "Ask a question": "Still et spørsmål", "Assistant": "Assistent", "Attach file": "Legg ved fil", "Attribute for Username": "Attributt for brukernavn", "Audio": "Lyd", "August": "august", "Authenticate": "<PERSON><PERSON><PERSON><PERSON>", "Auto-Copy Response to Clipboard": "Ko<PERSON>r svar automatisk til utklippstavlen", "Auto-playback response": "Spill av svar automatisk", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "API-Autentiseringsstreng for AUTOMATIC1111", "AUTOMATIC1111 Base URL": "Absolutt URL for AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Absolutt URL for AUTOMATIC1111 kreves.", "Available list": "Tilgjengelig liste", "available!": "tilgjengelig!", "Azure AI Speech": "Azure AI-tale", "Azure Region": "Azure område", "Back": "Tilbake", "Bad": "", "Bad Response": "<PERSON><PERSON><PERSON><PERSON> svar", "Banners": "<PERSON><PERSON>", "Base Model (From)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (fra)", "Batch Size (num_batch)": "Batchstørrelse (num_batch)", "before": "<PERSON>ør", "Beta": "", "Bing Search V7 Endpoint": "Endepunkt for Bing Search V7", "Bing Search V7 Subscription Key": "<PERSON>bon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for Bing Search V7", "Brave Search API Key": "API-n<PERSON>k<PERSON> for Brave Search", "By {{name}}": "Etter {{name}}", "Bypass SSL verification for Websites": "Omgå SSL-verifisering for nettsteder", "Call": "Ring", "Call feature is not supported when using Web STT engine": "Ringefunksjonen støttes ikke når du bruker Web STT-motoren", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Capabilities": "<PERSON><PERSON><PERSON><PERSON>", "Capture": "", "Certificate Path": "Sertifikatbane", "Change Password": "<PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "", "Chart new frontiers": "Kartlegg ny områder", "Chat": "Cha<PERSON>", "Chat Background Image": "Bakgrunnsbilde for chat", "Chat Bubble UI": "<PERSON><PERSON><PERSON><PERSON><PERSON> for chat-bob<PERSON>", "Chat Controls": "Kontrollere i chat", "Chat direction": "Retning på chat", "Chat Overview": "Chatoversikt", "Chat Permissions": "<PERSON><PERSON><PERSON><PERSON> for chat", "Chat Tags Auto-Generation": "Auto-generering av chatetiketter", "Chats": "Chatter", "Check Again": "Sjekk på nytt", "Check for updates": "Sjekk for oppdateringer", "Checking for updates...": "<PERSON><PERSON><PERSON> for oppdateringer ...", "Choose a model before saving...": "Velg en modell før du lagrer ...", "Chunk Overlap": "Chunk-overlapp", "Chunk Params": "Chunk-parametere", "Chunk Size": "Chunk-st<PERSON><PERSON><PERSON>", "Ciphers": "<PERSON><PERSON>", "Citation": "Kildehenvisning", "Clear memory": "<PERSON><PERSON><PERSON> minnet", "click here": "<PERSON><PERSON><PERSON> her", "Click here for filter guides.": "<PERSON><PERSON><PERSON> her for å få veiledning om filtre", "Click here for help.": "Klikk her for å få hjelp.", "Click here to": "K<PERSON>k her for å", "Click here to download user import template file.": "Klikk her for å hente ned malfilen for import av brukere.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON> her for å lære mer om faster-whisper, og se de tilgjengelige modellene.", "Click here to select": "K<PERSON>k her for å velge", "Click here to select a csv file.": "<PERSON><PERSON>k her for å velge en CSV-fil.", "Click here to select a py file.": "<PERSON><PERSON><PERSON> her for å velge en PY-fil.", "Click here to upload a workflow.json file.": "<PERSON><PERSON><PERSON> her for å laste opp en workflow.json-fil.", "click here.": "klikk her.", "Click on the user role button to change a user's role.": "Klikk på knappen Brukerrolle for å endre en brukers rolle.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Skrivetilgang til utklippstavlen avslått. Kontroller nettleserinnstillingene for å gi den nødvendige tilgangen.", "Clone": "Klon", "Close": "Lukk", "Code execution": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Code formatted successfully": "<PERSON>den er <PERSON>ert", "Collection": "<PERSON><PERSON>", "Color": "<PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "Absolutt URL for ComfyUI", "ComfyUI Base URL is required.": "Absolutt URL for ComfyUI kreves.", "ComfyUI Workflow": "ComfyUI-arbeidsflyt", "ComfyUI Workflow Nodes": "ComfyUI-arbeidsflytnoder", "Command": "Kommando", "Completions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON> fore<PERSON><PERSON><PERSON><PERSON>", "Configure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Configure Models": "Konfigurer modeller", "Confirm": "Bekreft", "Confirm Password": "Bekreft passordet", "Confirm your action": "Bekreft handlingen", "Confirm your new password": "", "Connections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Contact Admin for WebUI Access": "Kontakt administrator for å få tilgang til WebUI", "Content": "Innhold", "Content Extraction": "Uthenting av innhold", "Context Length": "Kontekstlengde", "Continue Response": "Fortsett svar", "Continue with {{provider}}": "Fortsett med {{provider}}", "Continue with Email": "Fortsett med e-post", "Continue with LDAP": "Fortsett med LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Kontrollerer hvordan meldingsteksten deles opp for TTS-forespørsler. 'Punctuation' deler opp i setninger, 'paragraphs' deler opp i avsnitt, og 'none' beholder meldingen som én enkelt streng.", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "<PERSON><PERSON><PERSON> balansen mellom sammenheng og mangfold i utdataene. En lavere verdi gir en mer fokusert og sammenhengende tekst. (Standard: 5.0)", "Copied": "<PERSON><PERSON><PERSON>", "Copied shared chat URL to clipboard!": "Kopierte delt chat-URL til utklippstavlen!", "Copied to clipboard": "Kopier til utklippstaveln", "Copy": "<PERSON><PERSON><PERSON>", "Copy last code block": "<PERSON><PERSON><PERSON> siste kode<PERSON>lo<PERSON>k", "Copy last response": "<PERSON><PERSON><PERSON> siste svar", "Copy Link": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Ko<PERSON>r til utklippstavle", "Copying to clipboard was successful!": "<PERSON><PERSON><PERSON> til utklippstavlen!", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Create a knowledge base": "Opprett en kunnskapsbase", "Create a model": "<PERSON><PERSON><PERSON><PERSON> en modell", "Create Account": "<PERSON><PERSON><PERSON><PERSON> konto", "Create Admin Account": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Create Channel": "", "Create Group": "Op<PERSON>rett gruppe", "Create Knowledge": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Create new key": "Lag ny nøkkel", "Create new secret key": "Lag ny hemmelig nøkkel", "Created at": "Opprettet", "Created At": "Opprettet", "Created by": "Opprettet av", "CSV Import": "CSV-import", "Current Model": "Nåværende modell", "Current Password": "Nåværende passord", "Custom": "Tilpasset", "Dark": "<PERSON><PERSON><PERSON>", "Database": "Database", "December": "desember", "Default": "Standard", "Default (Open AI)": "Standard (Open AI)", "Default (SentenceTransformers)": "Standard (SentenceTransformers)", "Default Model": "Standard modell", "Default model updated": "Standard modell oppdatert", "Default Models": "Standard modeller", "Default permissions": "Standard tillatelser", "Default permissions updated successfully": "Standard tillatelser oppdatert", "Default Prompt Suggestions": "Standard forslag til ledetekster", "Default to 389 or 636 if TLS is enabled": "Velg 389 eller 636 som standard hvis TLS er aktivert", "Default to ALL": "Velg ALL som standard", "Default User Role": "Standard brukerrolle", "Delete": "<PERSON><PERSON>", "Delete a model": "<PERSON><PERSON> en modell", "Delete All Chats": "<PERSON><PERSON> alle chatter", "Delete All Models": "<PERSON><PERSON> alle modeller", "Delete chat": "<PERSON><PERSON> chat", "Delete Chat": "<PERSON><PERSON> chat", "Delete chat?": "Slette chat?", "Delete folder?": "Slette mappe?", "Delete function?": "Slette funk<PERSON>jon?", "Delete Message": "", "Delete prompt?": "<PERSON><PERSON>?", "delete this link": "slett denne lenken", "Delete tool?": "Slette verktøy?", "Delete User": "<PERSON><PERSON> bruker", "Deleted {{deleteModelTag}}": "Slettet {{deleteModelTag}}", "Deleted {{name}}": "Slettet {{name}}", "Deleted User": "<PERSON><PERSON><PERSON> bruker", "Describe your knowledge base and objectives": "Beskriv kunnskapsbasen din og målene dine", "Description": "Beskrivelse", "Disabled": "Deaktivert", "Discover a function": "Oppdag en funksjon", "Discover a model": "Oppdag en modell", "Discover a prompt": "Oppdag en ledetekst", "Discover a tool": "Oppdag et verktøy", "Discover wonders": "Oppdag ", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON>, last ned og utforsk tilpassede funksjoner", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON>, last ned og utforsk tilpassede ledetekster", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON>, last ned og utforsk tilpassede verktøy", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, last ned og utforsk forhåndsinnstillinger for modeller", "Dismissible": "<PERSON>n lukkes", "Display": "V<PERSON><PERSON>", "Display Emoji in Call": "Vis emoji i samtale", "Display the username instead of You in the Chat": "Vis brukernavnet ditt i stedet for Du i chatten", "Displays citations in the response": "<PERSON>is kildehenvisninger i svaret", "Dive into knowledge": "<PERSON><PERSON> kjent med kunnskap", "Do not install functions from sources you do not fully trust.": "Ikke installer funk<PERSON><PERSON><PERSON> fra kilder du ikke stoler på.", "Do not install tools from sources you do not fully trust.": "Ikke installer verkt<PERSON>y fra kilder du ikke stoler på.", "Document": "Dokument", "Documentation": "Dokumentasjon", "Documents": "Do<PERSON><PERSON><PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "ikke ingen tilkobling til eksterne tjenester. <PERSON><PERSON> dine forblir sikkert på den lokale serveren.", "Don't have an account?": "Har du ingen konto?", "don't install random functions from sources you don't trust.": "ikke installer til<PERSON><PERSON> funksjoner fra kilder du ikke stoler på.", "don't install random tools from sources you don't trust.": "ikke installer til<PERSON>ige verktøy fra kilder du ikke stoler på.", "Done": "<PERSON><PERSON><PERSON>", "Download": "Last ned", "Download canceled": "Nedlasting a<PERSON><PERSON><PERSON><PERSON>", "Download Database": "Last ned database", "Drag and drop a file to upload or select a file to view": "<PERSON>a og slipp en fil for å laste den opp, eller velg en fil å vise den", "Draw": "Tegne", "Drop any files here to add to the conversation": "<PERSON><PERSON><PERSON> filer her for å legge dem til i samtalen", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "f.eks. '30s','10m'. <PERSON><PERSON><PERSON>ge tidsenheter er 's', 'm', 't'.", "e.g. A filter to remove profanity from text": "f.eks. et filter for å fjerne banning fra tekst", "e.g. My Filter": "f.e<PERSON><PERSON> filter", "e.g. My Tools": "f.eks. Mine verktøy", "e.g. my_filter": "f.eks. mitt_filter", "e.g. my_tools": "f.eks. mine_verktøy", "e.g. Tools for performing various operations": "f.e<PERSON><PERSON> for å gjøre ulike handlinger", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Rediger Arena-modell", "Edit Channel": "", "Edit Connection": "<PERSON><PERSON> til<PERSON>", "Edit Default Permissions": "Rediger standard tillatelser", "Edit Memory": "<PERSON>iger minne", "Edit User": "Rediger bruker", "Edit User Group": "Rediger brukergruppe", "ElevenLabs": "ElevenLabs", "Email": "E-postadresse", "Embark on adventures": "Kom med på eventyr", "Embedding Batch Size": "<PERSON><PERSON><PERSON>s<PERSON><PERSON><PERSON><PERSON> for innbygging", "Embedding Model": "Innbyggingsmodell", "Embedding Model Engine": "Motor for innbygging av modeller", "Embedding model set to \"{{embedding_model}}\"": "Innbyggingsmodell angitt til \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "Aktiver automatisk utfylling av chatmeldinger", "Enable Community Sharing": "Aktiver deling i fellesskap", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Aktiver Memory Locking (mlock) for å forhindre at modelldata byttes ut av RAM. Dette alternativet låser modellens arbeidssett med sider i RAM-minnet, slik at de ikke byttes ut til disk. Dette kan bidra til å opprettholde ytelsen ved å unngå sidefeil og sikre rask datatilgang.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Aktiver Memory Mapping (mmap) for å laste inn modelldata. Med dette alternativet kan systemet bruke disklagring som en utvidelse av RAM ved å behandle diskfiler som om de befant seg i RAM. Dette kan forbedre modellens ytelse ved å gi raskere datatilgang. Det er imidlertid ikke sikkert at det fungerer som det skal på alle systemer, og det kan kreve mye diskplass.", "Enable Message Rating": "Aktivert vurdering av meldinger", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "Aktiver Mirostat-sampling for kontroll av perpleksitet. (Standard: 0, 0 = deaktivert, 1 = Mirostat, 2 = Mirostat 2.0)", "Enable New Sign Ups": "Aktiver nye registreringer", "Enable Web Search": "Aktiver websøk", "Enabled": "Aktivert", "Engine": "Motor", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON> for at CSV-filen din inkluderer fire kolonner i denne rekkefølgen: Navn, E-post, Passord, Rolle.", "Enter {{role}} message here": "Skriv inn {{role}} melding her", "Enter a detail about yourself for your LLMs to recall": "Skriv inn en detalj om deg selv som språkmodellene dine kan huske", "Enter api auth string (e.g. username:password)": "Skriv inn API-autentiseringsstreng (f.eks. brukernavn:passord)", "Enter Application DN": "Angi applikasjonens DN", "Enter Application DN Password": "Angi applikasjonens DN-passord", "Enter Bing Search V7 Endpoint": "<PERSON><PERSON> endep<PERSON>t for Bing Search V7", "Enter Bing Search V7 Subscription Key": "<PERSON><PERSON> for Bing Search V7", "Enter Brave Search API Key": "Angi API-nøkkel for Brave Search", "Enter certificate path": "<PERSON><PERSON> sertifika<PERSON><PERSON> bane", "Enter CFG Scale (e.g. 7.0)": "Angi CFG-skala (f.eks. 7,0)", "Enter Chunk Overlap": "<PERSON><PERSON>-overlapp", "Enter Chunk Size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enter description": "<PERSON><PERSON>", "Enter Github Raw URL": "<PERSON><PERSON>-URL", "Enter Google PSE API Key": "Angi API-nøkkel for Google PSE", "Enter Google PSE Engine Id": "Angi motor-ID for Google PSE", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON> (f.eks. 512x512)", "Enter Jina API Key": "Angi API-n<PERSON><PERSON><PERSON> for Jina", "Enter Kagi Search API Key": "", "Enter language codes": "<PERSON><PERSON>", "Enter Model ID": "Angi modellens ID", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON> modellen<PERSON> etikett (f.eks. {{modelTag}})", "Enter Mojeek Search API Key": "Angi API-n<PERSON><PERSON><PERSON> for Mojeek-søk", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON> antall steg (f.eks. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON> (e.g. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON> (f.eks<PERSON>)", "Enter Score": "<PERSON><PERSON>", "Enter SearchApi API Key": "Angi API-nøkkel for SearchApi", "Enter SearchApi Engine": "Angi motor for SearchApi", "Enter Searxng Query URL": "<PERSON><PERSON>-URL for Searxng", "Enter Seed": "<PERSON><PERSON>", "Enter Serper API Key": "Angi API-nøkkel for Serper", "Enter Serply API Key": "Angi API-nøkkel for Serply", "Enter Serpstack API Key": "Angi API-n<PERSON>k<PERSON> for Serpstack", "Enter server host": "Angi server host", "Enter server label": "Angi server etikett", "Enter server port": "Angi server port", "Enter stop sequence": "<PERSON><PERSON>", "Enter system prompt": "Angi systemledetekst", "Enter Tavily API Key": "Angi API-n<PERSON><PERSON><PERSON> for Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Angi server-URL for Tika", "Enter Top K": "Angi Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "<PERSON><PERSON> URL (f.eks. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "<PERSON><PERSON> URL (f.eks. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Skriv inn e-postadressen din", "Enter Your Full Name": "Skriv inn det fulle navnet ditt", "Enter your message": "Skriv inn din melding", "Enter your new password": "", "Enter Your Password": "Skriv inn passordet ditt", "Enter your prompt": "", "Enter Your Role": "Skriv inn rollen din", "Enter Your Username": "Skriv inn brukernavnet ditt", "Enter your webhook URL": "", "Error": "<PERSON><PERSON>", "ERROR": "FEIL", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "<PERSON><PERSON><PERSON><PERSON>", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Eksempel: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Eksempel: ALL", "Example: ou=users,dc=foo,dc=example": "Eksempel: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Eksempel: sAMAccountName eller uid eller userPrincipalName", "Exclude": "Utelukk", "Experimental": "Eksperimentell", "Explore the cosmos": "Utforsk verdensrommet", "Export": "Eksporter", "Export All Archived Chats": "Eksporter alle arkiverte chatter", "Export All Chats (All Users)": "Eksporter alle chatter (alle brukere)", "Export chat (.json)": "Eksporter chat (.json)", "Export Chats": "Eksporter chatter", "Export Config to JSON File": "Ekporter konfigurasjon til en JSON-fil", "Export Functions": "Eksporter funksjoner", "Export Models": "Eksporter modeller", "Export Presets": "Eksporter forhåndsinnstillinger", "Export Prompts": "Eksporter ledetekster", "Export to CSV": "Eksporter til CSV", "Export Tools": "Eksporter verktøy", "External Models": "Eksterne modeller", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "Kan ikke legge til filen.", "Failed to create API Key.": "Kan ikke opprette en API-nøkkel.", "Failed to read clipboard contents": "Kan ikke lese utklippstavlens innhold", "Failed to save models configuration": "Kan ikke lagre konfigurasjonen av modeller", "Failed to update settings": "Kan ikke oppdatere innstillinger", "February": "februar", "Feedback History": "Tilbakemeldingslogg", "Feedbacks": "Tilbakemeldinger", "File": "Fil", "File added successfully.": "Filen er lagt til.", "File content updated successfully.": "Filens innhold er oppdatert.", "File Mode": "Filmodus", "File not found.": "Finner ikke filen.", "File removed successfully.": "<PERSON>n er fjernet.", "File size should not exceed {{maxSize}} MB.": "<PERSON>ls<PERSON><PERSON><PERSON><PERSON> kan ikke være på mer enn {{maxSize} MB", "File uploaded successfully": "", "Files": "Filer", "Filter is now globally disabled": "Filteret er nå globalt deaktivert", "Filter is now globally enabled": "Filteret er nå globalt aktivert", "Filters": "Filtre", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingeravtrykk-spoofing oppdaget: kan ikke bruke initialer som avatar. Bruker standard profilbilde.", "Fluidly stream large external response chunks": "Flytende strømming av store eksterne svarpakker", "Focus chat input": "Fokusert chat-inndata", "Folder deleted successfully": "<PERSON><PERSON> slettet", "Folder name cannot be empty": "Mappenavn kan ikke være tomt", "Folder name cannot be empty.": "Mappenavn kan ikke være tomt.", "Folder name updated successfully": "Mappenavn oppdatert", "Forge new paths": "<PERSON><PERSON> nye baner", "Form": "Form", "Format your variables using brackets like this:": "Formatér variablene dine med klammer som disse:", "Frequency Penalty": "Frekvensstraff", "Function": "Funksjon", "Function created successfully": "Funksjonen er opprettet", "Function deleted successfully": "Funksjonen er slettet", "Function Description": "Beskrivelse av funksjon", "Function ID": "Funksjonens ID", "Function is now globally disabled": "Funksjonen er nå deaktivert globalt", "Function is now globally enabled": "Funksjonen er nå aktivert globalt", "Function Name": "Funksjonens navn", "Function updated successfully": "Funksjonen er oppdatert", "Functions": "Funksjoner", "Functions allow arbitrary code execution": "Funksjoner tillater vilkårlig kodekjøring", "Functions allow arbitrary code execution.": "Funksjoner tillater vilkårlig kodekjøring.", "Functions imported successfully": "Funksjoner er importert", "General": "Generelt", "General Settings": "<PERSON><PERSON><PERSON> inn<PERSON>ill<PERSON>", "Generate Image": "<PERSON><PERSON> bilde", "Generating search query": "<PERSON><PERSON><PERSON>", "Get started": "Kom i gang", "Get started with {{WEBUI_NAME}}": "Kom i gang med {{WEBUI_NAME}}", "Global": "Globalt", "Good Response": "<PERSON><PERSON> svar", "Google Drive": "", "Google PSE API Key": "API-nøkkel for Google PSE", "Google PSE Engine Id": "Motor-ID for Google PSE", "Group created successfully": "Gruppe opprettet", "Group deleted successfully": "Gruppe slettet", "Group Description": "Beskrivelse av gruppe", "Group Name": "Navn på gruppe", "Group updated successfully": "Gruppe oppdatert", "Groups": "Grupper", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "t:mm a", "Haptic Feedback": "Haptisk tilbakemelding", "Harmful or offensive": "", "has no conversations.": "har ingen samtaler.", "Hello, {{name}}": "Hei, {{name}}!", "Help": "<PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Hjelp oss med å skape den beste fellesskapsledertavlen ved å dele tilbakemeldingshistorikken din.", "Hex Color": "Hex-farge", "Hex Color - Leave empty for default color": "Hex-farge – la stå tom for standard farge", "Hide": "Skjul", "Host": "Host", "How can I help you today?": "Hva kan jeg hjelpe deg med i dag?", "How would you rate this response?": "<PERSON><PERSON><PERSON> vurderer du dette svaret?", "Hybrid Search": "Hybrid-søk", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Jeg bekrefter at jeg har lest og forstår konsekvensene av mine handlinger. Jeg er klar over risikoen forbundet med å kjøre vilkårlig kode, og jeg har verifisert kildens pålitelighet.", "ID": "ID", "Ignite curiosity": "Vekk nysgjerrigheten", "Image Compression": "", "Image Generation (Experimental)": "Bildegenerering (eksperimentell)", "Image Generation Engine": "Bildegenereringsmotor", "Image Max Compression Size": "", "Image Settings": "<PERSON><PERSON>inn<PERSON><PERSON><PERSON>", "Images": "Bilder", "Import Chats": "Importer chatter", "Import Config from JSON File": "Importer konfigurasjon fra en JSON-fil", "Import Functions": "Importer funksjoner", "Import Models": "Importer modeller", "Import Presets": "Importer forhåndsinnstillinger", "Import Prompts": "Importer ledetekster", "Import Tools": "Importer verktøy", "Include": "<PERSON><PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "Inkluder flagget --api-auth når du kjører stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Inkluder flagget --api når du kjører stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "Påvirker hvor raskt algoritmen reagerer på tilbakemeldinger fra den genererte teksten. En lavere læringshastighet vil føre til langsommere justeringer, mens en høyere læringshastighet vil gjøre algoritmen mer responsiv. (Standard: 0,1)", "Info": "Info", "Input commands": "Inntast kommandoer", "Install from Github URL": "Installer fra GitHub-URL", "Instant Auto-Send After Voice Transcription": "Øyeblikkelig automatisk sending etter taletranskripsjon", "Interface": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Invalid file format.": "Ugyldig filformat.", "Invalid Tag": "<PERSON><PERSON><PERSON><PERSON>tt", "is typing...": "", "January": "januar", "Jina API Key": "API-n<PERSON><PERSON><PERSON> for Jina", "join our Discord for help.": "bli med i Discord-fellesskapet vårt for å få hjelp.", "JSON": "JSON", "JSON Preview": "Forhåndsvisning av JSON", "July": "juli", "June": "juni", "JWT Expiration": "JWT-utløp", "JWT Token": "JWT-token", "Kagi Search API Key": "", "Keep Alive": "Hold i live", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Keyboard shortcuts": "Hurtigtaster", "Knowledge": "Kunnska<PERSON>", "Knowledge Access": "Tilgang til kunnskap", "Knowledge created successfully.": "Kunnskap opprettet.", "Knowledge deleted successfully.": "Kunnskap slettet.", "Knowledge reset successfully.": "Tilbakestilling av kunnskap vellykket.", "Knowledge updated successfully": "Kunnskap oppdatert", "Label": "Etikett", "Landing Page Mode": "Modus for startside", "Language": "Språk", "Last Active": "Sist aktiv", "Last Modified": "<PERSON>st endret", "Last reply": "", "Latest users": "", "LDAP": "LDAP", "LDAP server updated": "LDAP-server oppdatert", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON>", "Leave empty for unlimited": "La stå tomt for ubegrenset", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "La stå tomt for å inkludere alle modeller fra endepunktet \"{{URL}}/api/tags\"", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "La stå tomt for å inkludere alle modeller fra endepunktet \"{{URL}}/api/models\"", "Leave empty to include all models or select specific models": "La stå tomt for å inkludere alle modeller", "Leave empty to use the default prompt, or enter a custom prompt": "La stå tomt for å bruke standard ledetekst, eller angi en tilpasset ledetekst", "Light": "Lys", "Listening...": "Lyt<PERSON> ...", "Local": "<PERSON><PERSON>", "Local Models": "Lokale modeller", "Lost": "Tapt", "LTR": "LTR", "Made by OpenWebUI Community": "Laget av OpenWebUI-fellesskapet", "Make sure to enclose them with": "<PERSON><PERSON><PERSON> for å omslutte dem med", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON> for å eksportere en workflow.json-fil i API-formatet fra ComfyUI.", "Manage": "Administrer", "Manage Arena Models": "Behandle Arena-modeller", "Manage Ollama": "Be<PERSON><PERSON>", "Manage Ollama API Connections": "Behandle API-tilkoblinger for Ollama", "Manage OpenAI API Connections": "Behandle API-tilkoblinger for OpenAPI", "Manage Pipelines": "Behandle pipelines", "March": "mars", "Max Tokens (num_predict)": "Maks antall tokener (num_predict)", "Max Upload Count": "<PERSON><PERSON> antall opp<PERSON>", "Max Upload Size": "<PERSON><PERSON> stø<PERSON>se på opplasting", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maksimalt tre modeller kan lastes ned samtidig. Prøv igjen senere.", "May": "mai", "Memories accessible by LLMs will be shown here.": "Språkmodellers tilgjengelige minner vises her.", "Memory": "<PERSON><PERSON>", "Memory added successfully": "<PERSON>ne lagt til", "Memory cleared successfully": "<PERSON><PERSON>ø<PERSON>", "Memory deleted successfully": "<PERSON><PERSON> s<PERSON>t", "Memory updated successfully": "<PERSON><PERSON>", "Merge Responses": "<PERSON><PERSON> svar", "Message rating should be enabled to use this feature": "V<PERSON><PERSON> av meldinger må være aktivert for å ta i bruk denne funksjonen", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Meldinger du sender etter at du har opprettet lenken, blir ikke delt. Brukere med URL-en vil kunne se den delte chatten.", "Min P": "<PERSON>", "Minimum Score": "Minimum poengsum", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "DD MMMM YYYY", "MMMM DD, YYYY HH:mm": "HH:mm DD MMMM YYYY", "MMMM DD, YYYY hh:mm:ss A": "hh:mm:ss A DD MMMM YYYY", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "<PERSON><PERSON> {{modelName}} er lastet ned.", "Model '{{modelTag}}' is already in queue for downloading.": "Modellen {{modelTag}} er allerede i nedlastingskøen.", "Model {{modelId}} not found": "Finner ikke modellen {{modelId}}", "Model {{modelName}} is not vision capable": "Modellen {{modelName}} er ikke egnet til visuelle data", "Model {{name}} is now {{status}}": "<PERSON><PERSON> {{name}} er nå {{status}}", "Model accepts image inputs": "Modellen godtar bildeinndata", "Model created successfully!": "<PERSON>len er opprettet!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modellfilsystembane oppdaget. Kan ikke fortsette fordi modellens kortnavn er påkrevd for oppdatering.", "Model Filtering": "Filtrering av modeller", "Model ID": "Modell-ID", "Model IDs": "Modell-ID-er", "Model Name": "<PERSON><PERSON>", "Model not selected": "Modell ikke valgt", "Model Params": "Modellparametere", "Model Permissions": "Modelltillatelser", "Model updated successfully": "Modell oppdatert", "Modelfile Content": "Modellfilinnhold", "Models": "Modeller", "Models Access": "Tilgang til modeller", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "mer", "More": "<PERSON><PERSON>", "Name": "Navn", "Name your knowledge base": "Gi kunnskapsbasen et navn", "New Chat": "Ny chat", "New folder": "", "New Password": "<PERSON><PERSON><PERSON> passord", "new-channel": "", "No content found": "Finner ikke noe innhold", "No content to speak": "Mangler innhold for tale", "No distance available": "Ingen avstand tilgjengelig", "No feedbacks found": "<PERSON><PERSON> ingen til<PERSON><PERSON><PERSON>er", "No file selected": "Ingen fil valgt", "No files found.": "Finner ingen filer", "No groups with access, add a group to grant access": "Ingen grupper med tilgang. Legg til en gruppe som skal ha tilgang.", "No HTML, CSS, or JavaScript content found.": "<PERSON>er ikke noe HTML, CSS- eller JavaScript-innhold.", "No knowledge found": "Finner ingen kunns<PERSON>per", "No model IDs": "Ingen modell-ID-er", "No models found": "Finner ingen modeller", "No models selected": "Ingen modeller er valgt", "No results found": "<PERSON>er ingen resultater", "No search query generated": "<PERSON><PERSON> søkespørringer er generert", "No source available": "<PERSON>gen kilde <PERSON>", "No users were found.": "Finner ingen brukere", "No valves to update": "Ingen ventiler å oppdatere", "None": "Ingen", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Merk: <PERSON><PERSON> du setter en minimumspoengsum, returnerer søket kun dokumenter med en poengsum som er større enn eller lik minimumspoengsummen.", "Notes": "Notater", "Notification Sound": "", "Notification Webhook": "", "Notifications": "<PERSON><PERSON><PERSON>", "November": "november", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth-ID", "October": "oktober", "Off": "Av", "Okay, Let's Go!": "OK, kjør på!", "OLED Dark": "OLED mørk", "Ollama": "Ollama", "Ollama API": "Ollama-API", "Ollama API disabled": "Ollama-API deaktivert", "Ollama API settings updated": "API-innstillinger for Ollama er oppdatert", "Ollama Version": "Ollama-versjon", "On": "Aktivert", "Only alphanumeric characters and hyphens are allowed": "Bare alfanumeriske tegn og bindestreker er tillatt", "Only alphanumeric characters and hyphens are allowed in the command string.": "Bare alfanumeriske tegn og bindestreker er tillatt i kommandostrengen.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON>e samlinger kan redigeres, eller lag en ny kunnskapsbase for å kunne redigere / legge til dokumenter.", "Only select users and groups with permission can access": "Bare utvalgte brukere og grupper med tillatelse kan få tilgang", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oi! Det ser ut som URL-en er ugyldig. Dobbeltsjekk, og prøv på nytt.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Oi! Det er fortsatt filer som lastes opp. Vent til opplastingen er ferdig.", "Oops! There was an error in the previous response.": "Oi! Det er en feil i det forrige svaret.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oi! Du bruker en ikke-støttet metode (bare frontend). Du må kjøre WebUI fra backend.", "Open in full screen": "Åpne i fullskjerm", "Open new chat": "Å<PERSON>ne ny chat", "Open WebUI uses faster-whisper internally.": "Open WebUI bruker faster-whisper internt.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI bruker SpeechT5 og CMU Arctic-høytalerinnbygginger", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI-versjonen (v{{OPEN_WEBUI_VERSION}}) er lavere enn den påkrevde versjonen (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI-API", "OpenAI API Config": "API-konfigurasjon for OpenAI", "OpenAI API Key is required.": "API-nøkkel for OpenAI kreves.", "OpenAI API settings updated": "API-innstillinger for OpenAI er oppdatert", "OpenAI URL/Key required.": "URL/nøkkel for OpenAI kreves.", "or": "eller", "Organize your users": "Organisere brukerne dine", "OUTPUT": "UTDATA", "Output format": "Format på utdata", "Overview": "Oversikt", "page": "side", "Password": "Passord", "Paste Large Text as File": "Lim inn mye tekst som fil", "PDF document (.pdf)": "PDF-dokument (.pdf)", "PDF Extract Images (OCR)": "Uthenting av PDF-bilder (OCR)", "pending": "a<PERSON><PERSON><PERSON>", "Permission denied when accessing media devices": "Tilgang avslått ved bruk av medieenheter", "Permission denied when accessing microphone": "Tilgang avslått ved bruk av mikrofonen", "Permission denied when accessing microphone: {{error}}": "Tilgang avslått ved bruk av mikrofonen: {{error}}", "Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Personalization": "Tilpassing", "Pin": "Fest", "Pinned": "Festet", "Pioneer insights": "Nyskapende innsikt", "Pipeline deleted successfully": "Pipeline slettet", "Pipeline downloaded successfully": "Pipeline lastet ned", "Pipelines": "Pipelines", "Pipelines Not Detected": "Ingen pipelines oppdaget", "Pipelines Valves": "Pipeline-ventiler", "Plain text (.txt)": "<PERSON> tekst (.txt)", "Playground": "<PERSON><PERSON><PERSON><PERSON>", "Please carefully review the following warnings:": "Les gjennom følgende advarsler grundig:", "Please enter a prompt": "Angi en ledetekst", "Please fill in all fields.": "Fyll i alle felter", "Please select a model first.": "", "Port": "Port", "Prefix ID": "Prefiks-ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefiks-ID brukes for å unngå konflikter med andre tilkoblinger ved å legge til et prefiks til modell-ID-ene. La det stå tomt for å deaktivere", "Previous 30 days": "Siste 30 dager", "Previous 7 days": "Siste 7 dager", "Profile Image": "Profilbilde", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Ledetekst (f.eks. <PERSON> meg noe morsomt om romerriket)", "Prompt Content": "Ledetekstinnhold", "Prompt created successfully": "Ledetekst opprettet", "Prompt suggestions": "Forslag til ledetekst", "Prompt updated successfully": "Ledetekst oppdatert", "Prompts": "Ledetekster", "Prompts Access": "Tilgang til ledetekster", "Provide any specific details": "", "Proxy URL": "Proxy-URL", "Pull \"{{searchValue}}\" from Ollama.com": "Hent {{searchValue}} fra Ollama.com", "Pull a model from Ollama.com": "Hent en modell fra Ollama.com", "Query Generation Prompt": "Ledetekst for genering av spø<PERSON>er", "Query Params": "Spørringsparametere", "RAG Template": "RAG-mal", "Rating": "Vurdering", "Re-rank models by topic similarity": "Ny rangering av modeller etter emnelikhet", "Read Aloud": "<PERSON>", "Record voice": "Ta opp tale", "Redirecting you to OpenWebUI Community": "Omdirigerer deg til OpenWebUI-fellesskapet", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "Reduserer sannsynligheten for å generere meningsløse svar. En høyere verdi (f.eks. 100) vil gi mer varierte svar, mens en lavere verdi (f.eks. 10) vil være mer konservativ. (Standard: 40)", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "O<PERSON><PERSON> deg selv som \"Bruker\" (f.eks. \"Bruker lærer spansk\")", "References from": "Hen<PERSON>r fra", "Refresh Token Expiration": "", "Regenerate": "Generer på nytt", "Release Notes": "Utgivelsesnotater", "Relevance": "<PERSON><PERSON><PERSON>", "Remove": "<PERSON><PERSON><PERSON>", "Remove Model": "<PERSON><PERSON><PERSON> modell", "Rename": "Gi nytt navn", "Reorder Models": "Sorter modeller på nytt", "Repeat Last N": "G<PERSON>nta siste <PERSON>", "Reply in Thread": "", "Request Mode": "Forespørselsmodus", "Reranking Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reranking model disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> deaktivert", "Reranking model set to \"{{reranking_model}}\"": "Omrangeringsmodell er angitt til \"{{reranking_model}}\"", "Reset": "Tilbakestill", "Reset All Models": "", "Reset Upload Directory": "Tilbakestill opplastingskatalog", "Reset Vector Storage/Knowledge": "Tilbakestill Vector-lagring/kunnskap", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Svar-var<PERSON> kan ikke aktiveres fordi tilgang til nettstedet er nektet. Gå til nettleserinnstillingene dine for å gi den nødvendige tilgangen.", "Response splitting": "Oppdeling av svar", "Result": "Resultat", "Retrieval Query Generation": "", "Rich Text Input for Chat": "<PERSON><PERSON> for chat", "RK": "RK", "Role": "<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON><PERSON><PERSON>", "Save": "Lagre", "Save & Create": "Lagre og opprett", "Save & Update": "Lagre og oppdater", "Save As Copy": "Lagre som kopi", "Save Tag": "<PERSON><PERSON><PERSON><PERSON>", "Saved": "<PERSON>g<PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Lagring av chattelogger direkte til nettleserens lagringsområde støttes ikke lenger. Ta et øyeblikk til å laste ned og slette chatteloggende dine ved å klikke på knappen nedenfor. <PERSON><PERSON><PERSON> be<PERSON><PERSON> deg, du kan enkelt importere chatteloggene dine til backend på nytt via", "Scroll to bottom when switching between branches": "<PERSON>la til bunnen når du bytter mellom grener", "Search": "<PERSON><PERSON><PERSON>", "Search a model": "<PERSON><PERSON><PERSON> etter en modell", "Search Base": "Søke etter base", "Search Chats": "<PERSON><PERSON><PERSON> etter chatter", "Search Collection": "<PERSON><PERSON><PERSON> etter samling", "Search Filters": "<PERSON><PERSON><PERSON> etter filtre", "search for tags": "søk etter etiketter", "Search Functions": "<PERSON><PERSON><PERSON> etter <PERSON>", "Search Knowledge": "<PERSON><PERSON><PERSON> etter kun<PERSON>", "Search Models": "<PERSON><PERSON><PERSON> etter modeller", "Search options": "<PERSON><PERSON><PERSON> etter alternativer", "Search Prompts": "<PERSON><PERSON><PERSON> etter <PERSON>", "Search Result Count": "<PERSON><PERSON><PERSON> søkeresultater", "Search Tools": "Søkeverktøy", "Search users": "", "SearchApi API Key": "API-nøkkel for SearchApi", "SearchApi Engine": "Motor for SearchApi", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> et<PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> etter kun<PERSON> for \"{{searchQuery}}\"", "Searxng Query URL": "Searxng forespørsels-URL", "See readme.md for instructions": "Se readme.md for å få instruksjoner", "See what's new": "Se hva som er nytt", "Seed": "Seed", "Select a base model": "Velg en grunnmodell", "Select a engine": "Velg en motor", "Select a function": "Velg en funksjon", "Select a group": "Velg en gruppe", "Select a model": "Velg en modell", "Select a pipeline": "Velg en pipeline", "Select a pipeline url": "Velg en pipeline-URL", "Select a tool": "Velg et verktøy", "Select Engine": "Velg motor", "Select Knowledge": "<PERSON><PERSON>g kun<PERSON>", "Select model": "Velg modell", "Select only one model to call": "Velg bare én modell som skal kalles", "Selected model(s) do not support image inputs": "Valgte modell(er) støtter ikke bildeinndata", "Semantic distance to query": "Semantisk distanse til spørring", "Send": "Send", "Send a message": "", "Send a Message": "Send en melding", "Send message": "Send melding", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Sender `stream_options: { include_usage: true }` i forespørselen.\nStøttede leverandører returnerer informasjon i svaret om bruk av token når denne parameteren er angitt.", "September": "september", "Serper API Key": "API-n<PERSON>k<PERSON> for Serper", "Serply API Key": "API-n<PERSON>k<PERSON> for Serply", "Serpstack API Key": "API-n<PERSON><PERSON><PERSON> for Serpstack", "Server connection verified": "Servertilkobling bekreftet", "Set as default": "Angi som standard", "Set CFG Scale": "Angi CFG-skala", "Set Default Model": "Angi standard modell", "Set embedding model": "<PERSON><PERSON> inn<PERSON><PERSON>", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON> (f.eks. {{model}})", "Set Image Size": "<PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON> modell for omrangering (f.eks. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set Scheduler": "<PERSON><PERSON> planlegger", "Set Steps": "<PERSON><PERSON> steg", "Set Task Model": "<PERSON><PERSON>", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "<PERSON><PERSON> antall GPU-enheter som brukes til beregning. <PERSON>te alternativet styrer hvor mange GPU-enheter (hvis til<PERSON>g) som brukes til å behandle innkommende forespørsler. <PERSON><PERSON> du øker denne verdien, kan du forbedre y<PERSON>sen betydeli<PERSON> for modeller som er optimalisert for GPU-akselerasjon, men det kan også føre til at det brukes mer strøm og GPU-ressurser.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "<PERSON>i antall arbeidstråder som skal brukes til beregning. Dette alternativet kontrollerer hvor mange tråder som brukes til å behandle innkommende forespørsler samtidig. <PERSON><PERSON> du øker denne verdien, kan det forbedre ytelsen under arbeidsbelastninger med høy samtidighet, men det kan også føre til økt forbruk av CPU-ressurser.", "Set Voice": "<PERSON><PERSON> stemme", "Set whisper model": "<PERSON><PERSON> whisper-modell", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "<PERSON>ir hvor langt tilbake modellen skal se for å forhindre repetisjon. (Standard: 64, 0 = deaktivert, -1 = num_ctx)", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "Angir hvor sterkt repetisjoner skal straffes. En høyere verdi (f.eks. 1,5) vil straffe gjentakelser hardere, mens en lavere verdi (f.eks. 0,9) vil være mildere. (Standard: 1,1)", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "<PERSON>ir det tilfeldige tallfrøet som skal brukes til generering. <PERSON><PERSON> du setter dette til et bestemt tall, vil modellen generere den samme teksten for den samme ledeteksten (standard: tilfeldig).", "Sets the size of the context window used to generate the next token. (Default: 2048)": "<PERSON><PERSON> stø<PERSON> på kontekstvinduet som brukes til å generere neste token. (Standard: 2048)", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Angir hvilke stoppsekvenser som skal brukes. <PERSON><PERSON><PERSON> dette mønsteret forekommer, stopper LLM genereringen av tekst og returnerer. Du kan angi flere stoppmønstre ved å spesifisere flere separate stoppparametere i en modellfil.", "Settings": "Innstillinger", "Settings saved successfully!": "Innstillinger lagret!", "Share": "Del", "Share Chat": "Del chat", "Share to OpenWebUI Community": "Del med OpenWebUI-fellesskapet", "Show": "Vis", "Show \"What's New\" modal on login": "Vis \"Hva er nytt\"-modal ved innlogging", "Show Admin Details in Account Pending Overlay": "<PERSON>is <PERSON> i ventende kontovisning", "Show shortcuts": "<PERSON><PERSON> s<PERSON>", "Show your support!": "Vis din støtte!", "Sign in": "Logg inn", "Sign in to {{WEBUI_NAME}}": "<PERSON>gg på {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Logg på {{WEBUI_NAME}} med LDAP", "Sign Out": "Logg ut", "Sign up": "Registrer deg", "Sign up to {{WEBUI_NAME}}": "Registrer deg for {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "<PERSON><PERSON> på {{WEBUI_NAME}}", "sk-1234": "", "Source": "<PERSON><PERSON>", "Speech Playback Speed": "Has<PERSON>ghet på avspilling av tale", "Speech recognition error: {{error}}": "<PERSON>il ved taleg<PERSON><PERSON><PERSON>: {{error}}", "Speech-to-Text Engine": "Motor for Tale-til-tekst", "Stop": "Stopp", "Stop Sequence": "Stoppsekvens", "Stream Chat Response": "Strømme chat-svar", "STT Model": "STT-modell", "STT Settings": "STT-innstillinger", "Success": "<PERSON><PERSON><PERSON>", "Successfully updated.": "Oppdatert.", "Suggested prompts to get you started": "", "Support": "Bidra", "Support this plugin:": "Bidra til denne utvidelsen:", "Sync directory": "Synkroniseringsmappe", "System": "System", "System Instructions": "Systeminstruksjoner", "System Prompt": "Systemledetekst", "Tags Generation": "", "Tags Generation Prompt": "Ledetekst for genering av etikett", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "Tail free sampling brukes til å redusere innvirkningen av mindre sannsynlige tokens fra utdataene. En høyere verdi (f.eks. 2,0) vil redusere effekten mer, mens en verdi på 1,0 deaktiverer denne innstillingen. (standard: 1)", "Tap to interrupt": "Trykk for å avbryte", "Tavily API Key": "API-n<PERSON><PERSON><PERSON> for Tavily", "Temperature": "Temperatur", "Template": "Mal", "Temporary Chat": "<PERSON><PERSON><PERSON>dig chat", "Text Splitter": "Oppdeling av tekst", "Text-to-Speech Engine": "Tekst-til-tale-motor", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Takk for tilbakemeldingen!", "The Application Account DN you bind with for search": "Applikasjonskontoens DN du binder deg med for søking", "The base to search for users": "Basen for å søke etter brukere", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> avgjør hvor mange tekstforespørsler som behandles samtidig. En høyere batchstørrelse kan øke ytelsen og hastigheten til modellen, men det krever også mer minne. (Standard: 512)", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Utviklerne bak denne utvidelsen er lidenskapelige frivillige fra fellesskapet. Hvis du finner denne utvidelsen nyttig, vennligst vurder å bidra til utviklingen.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "<PERSON><PERSON><PERSON><PERSON><PERSON> over evalueringer er basert på Elo-rangeringssystemet, og oppdateres i sanntid.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP-attributtet som tilsvarer brukernavnet som brukerne bruker for å logge på.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Ledertavlen er for øyeblikket i betaversjon, og vi kommer kanskje til å justere beregningene etter hvert som vi forbedrer algoritmen.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Den maksimale filstørrelsen i MB. Hvis en filstørrelse overskrider denne g<PERSON>, blir ikke filen lastet opp.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Maksimalt antall filer som kan brukes samtidig i chatten. Hvis antallet filer overskrider denne g<PERSON>, blir de ikke lastet opp.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Poengsummen skal være en verdi mellom 0,0 (0 %) og 1,0 (100 %).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "Temperaturen på modellen. <PERSON><PERSON> du øker temperaturen, vil modellen svare mer kreativt. (Standard: 0,8)", "Theme": "<PERSON><PERSON>", "Thinking...": "Tenker ...", "This action cannot be undone. Do you wish to continue?": "<PERSON>ne <PERSON>en kan ikke angres. Vil du fortsette?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> sikrer at de verdifulle samtalene dine lagres sikkert i backend-databasen din. Takk!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Dette er en eksperimentell funksjon. Det er mulig den ikke fungerer som forventet, og den kan endres når som helst.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "Dette alternativet styrer hvor mange tokens som bevares når konteksten oppdateres. Hvis det for eksempel er angitt til 2, beholdes de to siste symbolene i samtalekonteksten. Bevaring av konteksten kan bidra til å opprettholde kontinuiteten i en samtale, men det kan redusere muligheten til å svare på nye emner. (Standard: 24)", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "Dette alternativet angir det maksimale antallet tokens modellen kan generere i svaret sitt. <PERSON><PERSON> du øker denne grensen, kan modellen gi lengre svar, men det kan også øke sannsynligheten for at det genereres uhensiktsmessig eller irrelevant innhold. (Standard: 128)", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Dette alternativet sletter alle eksisterende filer i samlingen og erstatter dem med nyopplastede filer.", "This response was generated by \"{{model}}\"": "<PERSON>te svaret er generert av \"{{modell}}\"", "This will delete": "<PERSON><PERSON> sletter", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Dette sletter <strong>{{NAME}}</strong> og <strong>alt innholdet</strong>.", "This will delete all models including custom models": "<PERSON><PERSON> sletter alle modeller, inkludert tilpassede modeller", "This will delete all models including custom models and cannot be undone.": "<PERSON><PERSON> sletter alle modeller, inkludert tilpassede modeller, og kan ikke angres.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Dette tilbakestiller kunnskapsbasen og synkroniserer alle filer. Vil du fortsette?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Server-URL for Tika kreves.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tips: <PERSON><PERSON><PERSON><PERSON> flere variabelplasser etter hverandre ved å trykke på TAB-tasten i chat-inntastingsfeltet etter hver erstatning.", "Title": "<PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON>ittel (f.eks. <PERSON> meg noe morsomt)", "Title Auto-Generation": "Automatisk tittelgenerering", "Title cannot be an empty string.": "<PERSON>ittel kan ikke være en tom streng.", "Title Generation Prompt": "Ledetekst for tittelgenerering", "TLS": "TLS", "To access the available model names for downloading,": "<PERSON><PERSON> du vil ha tilgang til modellnavn tilgjengelige for nedlasting,", "To access the GGUF models available for downloading,": "<PERSON><PERSON> du vil ha tilgang til GGUF-modellene tilg<PERSON>lige for nedlasting,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "<PERSON><PERSON> du vil ha tilgang til WebUI, må du kontakte administrator. Administratorer kan behandle brukeres status fra Admin-panelet.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "<PERSON>vis du vil legge til kunnskapsbaser her, må du først legge dem til i arbeidsområdet \"Kunnskap\".", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "For å beskytte personvernet ditt deles bare vurderinger, modell-ID-er, etiketter og metadata fra dine tilbakemeldinger. Chattelogger forblir private og inkluderes ikke.", "To select actions here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON> du vil velge handlinger her, må du først legge dem til i arbeidsområdet \"Funksjoner\".", "To select filters here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON> du vil velge filtre her, må du først legge dem til i arbeidsområdet \"Funksjoner\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "<PERSON><PERSON> du vil velge verktø<PERSON>ett her, må du først legge dem til i arbeidsområdet \"Verktøy\".", "Toast notifications for new updates": "Hurtigmelding-notifikasjon for nye oppdateringer", "Today": "I dag", "Toggle settings": "Veksle innstillinger", "Toggle sidebar": "Veksle sidefelt", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "Tokens som skal beholdes ved kontekstoppdatering (num_keep)", "Tool created successfully": "Verktøy opprettet", "Tool deleted successfully": "Verkt<PERSON><PERSON> s<PERSON>t", "Tool Description": "Verktøyets beskrivelse", "Tool ID": "Verktøyets ID", "Tool imported successfully": "Verktøy importert", "Tool Name": "Verktøyets navn", "Tool updated successfully": "Verktøy oppdatert", "Tools": "Verktøy", "Tools Access": "Verktøyets tilgang", "Tools are a function calling system with arbitrary code execution": "Verktøy er et funksjonskallsystem med vilkårlig kodekjøring", "Tools have a function calling system that allows arbitrary code execution": "Verktøy inneholder et funksjonskallsystem som tillater vilkårlig kodekjøring", "Tools have a function calling system that allows arbitrary code execution.": "Verktøy inneholder et funksjonskallsystem som tillater vilkårlig kodekjøring.", "Top K": "Top K", "Top P": "Top P", "Transformers": "Transformatorer", "Trouble accessing Ollama?": "Problemer med å koble til Ollama?", "TTS Model": "TTS-modell", "TTS Settings": "TTS-innstillinger", "TTS Voice": "TTS-stemme", "Type": "Type", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON>-Resolve-URL for Hugging Face", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "Opphev arkiveringen av alle", "Unarchive All Archived Chats": "Opphev arkiveringen av alle arkiverte chatter", "Unarchive Chat": "Opphev arkivering av chat", "Unlock mysteries": "<PERSON><PERSON><PERSON> opp mysterier", "Unpin": "<PERSON><PERSON><PERSON><PERSON>", "Unravel secrets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Untagged": "<PERSON>kke merket", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Oppdater og kopier lenke", "Update for the latest features and improvements.": "<PERSON><PERSON><PERSON><PERSON> for å få siste funksjoner og forbedringer.", "Update password": "<PERSON><PERSON><PERSON><PERSON> passord", "Updated": "Oppdatert", "Updated at": "Oppdatert", "Updated At": "Oppdatert", "Upload": "Last opp", "Upload a GGUF model": "Last opp en GGUF-modell", "Upload directory": "Mappe for opplastinger", "Upload files": "Last opp filer", "Upload Files": "Last opp filer", "Upload Pipeline": "Last opp pipeline", "Upload Progress": "Opplastingsfremdrift", "URL": "URL", "URL Mode": "URL-modus", "Use '#' in the prompt input to load and include your knowledge.": "Bruk # i ledetekstens inntastingsfelt for å laste inn og inkludere kunnskapene dine.", "Use Gravatar": "Bruk Gravatar", "Use groups to group your users and assign permissions.": "Bruk grupper til å samle brukere og tildele tillatelser.", "Use Initials": "Bruk initialer", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "bruker", "User": "<PERSON><PERSON><PERSON>", "User location successfully retrieved.": "Brukerens lokasjon hentet", "Username": "Brukernavn", "Users": "<PERSON><PERSON><PERSON>", "Using the default arena model with all models. Click the plus button to add custom models.": "Bruker standard Arena-modellen med alle modeller. Klikk på plussknappen for å legge til egne modeller.", "Utilize": "Bruk", "Valid time units:": "Gyldige tidsenheter:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "Ventiler oppdatert", "Valves updated successfully": "Ventilene er oppdatert", "variable": "variabel", "variable to have them replaced with clipboard content.": "variabel for å erstatte dem med utklippstavleinnhold.", "Version": "Versjon", "Version {{selectedVersion}} of {{totalVersions}}": "Version {{selectedVersion}} av {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "Synlighet", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "Taleinndata", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "<PERSON><PERSON><PERSON>!", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Advarsel: <PERSON><PERSON> du aktiverer denne <PERSON>, kan brukere laste opp vilkårlig kode på serveren.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Advarsel: <PERSON><PERSON> du oppdaterer eller endrer innbyggingsmodellen din, må du importere alle dokumenter på nytt.", "Web": "Web", "Web API": "Web-API", "Web Loader Settings": "Web-<PERSON><PERSON><PERSON>st<PERSON><PERSON>", "Web Search": "Nettsøk", "Web Search Engine": "Nettsøkmotor", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "Innstillinger for WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI vil rette forespø<PERSON><PERSON> til \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI vil rette forespø<PERSON><PERSON> til \"{{url}}/chat/completions\"", "Welcome, {{name}}!": "", "What are you trying to achieve?": "Hva prøver du å oppnå?", "What are you working on?": "<PERSON>va jobber du på nå?", "What didn't you like about this response?": "", "What’s New in": "<PERSON>va er nytt i", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON> denne modusen er aktivert, svarer modellen på alle chattemeldinger i sanntid, og genererer et svar så snart brukeren sender en melding. Denne modusen er nyttig for live chat-applik<PERSON><PERSON>er, men kan påvirke ytelsen på tregere maskinvare.", "wherever you are": "u<PERSON>ett hvor du er", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Lokal)", "Widescreen Mode": "Bredskjermmodus", "Won": "<PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "<PERSON><PERSON><PERSON> sammen med top-k. <PERSON> høyere verdi (f.eks. 0,95) vil føre til mer mangfoldig tekst, mens en lavere verdi (f.eks. 0,5) vil generere mer fokusert og konservativ tekst. (Standard: 0,9)", "Workspace": "Arbeidsområde", "Workspace Permissions": "<PERSON><PERSON><PERSON><PERSON> for arbeidsområde", "Write a prompt suggestion (e.g. Who are you?)": "Skriv inn et forslag til ledetekst (f.eks. Hvem er du?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Skriv inn et sammendrag på 50 ord som oppsummerer [em<PERSON> eller nø<PERSON>].", "Write something...": "Skriv inn noe...", "Write your model template content here": "Skriv inn modellens malinnhold her", "Yesterday": "<PERSON> går", "You": "<PERSON>", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Du kan bare chatte med maksimalt {{maxCount}} fil(er) om gangen.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Du kan tilpasse interaksjonene dine med språkmodeller ved å legge til minner gjennom Administrer-knappen nedenfor, slik at de blir mer til nyttige og tilpasset deg.", "You cannot upload an empty file.": "Du kan ikke laste opp en tom fil.", "You have no archived conversations.": "Du har ingen arkiverte samtaler.", "You have shared this chat": "Du har delt denne chatten", "You're a helpful assistant.": "Du er en nyttig assistent.", "Your account status is currently pending activation.": "Status på kontoen din er for øyeblikket ventende på aktivering.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON> beløpet går uavkortet til utvikleren av tillegget. Open WebUI mottar ikke deler av beløpet. Den valgte betalingsplattformen kan ha gebyrer.", "Youtube": "Youtube", "Youtube Loader Settings": "<PERSON><PERSON><PERSON><PERSON> for YouTube-laster"}