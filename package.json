{"name": "open-webui", "version": "0.5.3", "private": true, "scripts": {"dev": "vite dev --host", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "npm run lint:frontend ; npm run lint:types ; npm run lint:backend", "lint:frontend": "eslint . --fix", "lint:types": "npm run check", "lint:backend": "pylint backend/", "format": "prettier --write \"**/*.{js,ts,svelte,css,md,html,json}\"", "format:backend": "black . --exclude \".venv/|/venv/\"", "i18n:parse": "i18next --config i18next-parser.config.ts && prettier --write \"src/lib/i18n/**/*.{js,json}\"", "cy:open": "cypress open", "test:frontend": "vitest --passWithNoTests", "pyodide:fetch": "node scripts/prepare-pyodide.js", "prepare": "husky"}, "devDependencies": {"@sveltejs/adapter-auto": "3.2.2", "@sveltejs/adapter-static": "^3.0.2", "@sveltejs/kit": "^2.20.7", "@sveltejs/vite-plugin-svelte": "^3.1.1", "@tailwindcss/typography": "^0.5.13", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "autoprefixer": "^10.4.16", "chokidar": "^4.0.3", "cypress": "^13.15.0", "esbuild": "^0.25.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^3.4.0", "eslint-plugin-svelte": "^2.43.0", "fs-extra": "^11.3.0", "glob": "^11.0.1", "husky": "9.1.7", "i18next-parser": "^9.3.0", "postcss": "^8.4.31", "prettier": "^3.3.3", "prettier-plugin-svelte": "^3.2.6", "sass-embedded": "^1.81.0", "svelte": "^4.2.18", "svelte-check": "^3.8.5", "svelte-confetti": "^1.3.2", "tailwindcss": "^3.3.3", "tslib": "^2.4.1", "typescript": "^5.5.4", "vite": "^5.4.19", "vitest": "^3.1.2"}, "type": "module", "dependencies": {"@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/language-data": "^6.5.1", "@codemirror/theme-one-dark": "^6.1.2", "@datadog/browser-logs": "^6.3.0", "@datadog/browser-rum": "^6.3.0", "@fontsource/space-mono": "^5.2.8", "@gsa-tts/graymatter-style-tokens": "^0.2.0", "@gsa-tts/graymatter-ui": "^0.2.1", "@huggingface/transformers": "^3.0.0", "@mediapipe/tasks-vision": "^0.10.17", "@pyscript/core": "^0.4.32", "@sveltejs/adapter-node": "^2.0.0", "@sveltejs/svelte-virtual-list": "^3.0.1", "@tiptap/core": "^2.10.0", "@tiptap/extension-code-block-lowlight": "^2.10.0", "@tiptap/extension-highlight": "^2.10.0", "@tiptap/extension-placeholder": "^2.10.0", "@tiptap/extension-typography": "^2.10.0", "@tiptap/pm": "^2.10.0", "@tiptap/starter-kit": "^2.10.0", "@types/fs-extra": "^11.0.4", "@xyflow/svelte": "^0.1.19", "async": "^3.2.5", "bits-ui": "^0.19.7", "codemirror": "^6.0.1", "codemirror-lang-hcl": "^0.0.0-beta.2", "crc-32": "^1.2.2", "dayjs": "^1.11.10", "dompurify": "^3.2.5", "eventsource-parser": "^1.1.2", "file-saver": "^2.0.5", "focus-trap": "^7.6.4", "fuse.js": "^7.0.0", "highlight.js": "^11.9.0", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^7.2.0", "i18next-resources-to-backend": "^1.2.0", "idb": "^7.1.1", "js-sha256": "^0.10.1", "katex": "^0.16.22", "marked": "^9.1.0", "mermaid": "^11.6.0", "paneforge": "^0.0.6", "panzoom": "^9.4.3", "prosemirror-commands": "^1.6.0", "prosemirror-example-setup": "^1.2.3", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.2", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.3", "pyodide": "^0.26.1", "socket.io-client": "^4.2.0", "sortablejs": "^1.15.2", "svelte-sonner": "^0.3.19", "tippy.js": "^6.3.7", "turndown": "^7.2.0", "uuid": "^9.0.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.34.8"}, "engines": {"node": ">=18.13.0 <=22.x.x", "npm": ">=6.0.0"}}